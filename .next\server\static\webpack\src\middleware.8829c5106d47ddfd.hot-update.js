"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("src/middleware",{

/***/ "(middleware)/./src/i18n.ts":
/*!*********************!*\
  !*** ./src/i18n.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   defaultLocale: () => (/* binding */ defaultLocale),\n/* harmony export */   formatConfig: () => (/* binding */ formatConfig),\n/* harmony export */   languageConfig: () => (/* binding */ languageConfig),\n/* harmony export */   locales: () => (/* binding */ locales),\n/* harmony export */   pathnames: () => (/* binding */ pathnames),\n/* harmony export */   phase2Locales: () => (/* binding */ phase2Locales)\n/* harmony export */ });\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/navigation */ \"(middleware)/./node_modules/next/dist/esm/api/navigation.js\");\n/* harmony import */ var next_intl_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-intl/server */ \"(middleware)/./node_modules/next-intl/dist/development/server.react-client.js\");\n\n\n// 第一阶段：核心市场语言 (立即实施)\nconst locales = [\n    \"en\",\n    \"zh\",\n    \"es\",\n    \"pt\",\n    \"hi\",\n    \"ja\"\n];\n// 第二阶段：重要区域语言 (6个月后扩展)\nconst phase2Locales = [\n    \"de\",\n    \"fr\",\n    \"it\",\n    \"ru\",\n    \"ko\",\n    \"ar\"\n];\n// 语言配置 - 包含完整的多语言元数据\nconst languageConfig = {\n    en: {\n        name: \"English\",\n        nativeName: \"English\",\n        direction: \"ltr\",\n        region: \"Global\",\n        population: \"Global market\",\n        flag: \"\\uD83C\\uDDFA\\uD83C\\uDDF8\",\n        hreflang: \"en\",\n        locale: \"en-US\",\n        currency: \"USD\",\n        fontFamily: \"latin\",\n        expansionFactor: 1.0,\n        priority: 1\n    },\n    zh: {\n        name: \"Chinese\",\n        nativeName: \"中文\",\n        direction: \"ltr\",\n        region: \"China & Chinese communities\",\n        population: \"1.4 billion\",\n        flag: \"\\uD83C\\uDDE8\\uD83C\\uDDF3\",\n        hreflang: \"zh-CN\",\n        locale: \"zh-CN\",\n        currency: \"CNY\",\n        fontFamily: \"chinese\",\n        expansionFactor: 0.8,\n        priority: 2\n    },\n    es: {\n        name: \"Spanish\",\n        nativeName: \"Espa\\xf1ol\",\n        direction: \"ltr\",\n        region: \"Spain & Latin America\",\n        population: \"500 million\",\n        flag: \"\\uD83C\\uDDEA\\uD83C\\uDDF8\",\n        hreflang: \"es\",\n        locale: \"es-ES\",\n        currency: \"EUR\",\n        fontFamily: \"latin\",\n        expansionFactor: 1.25,\n        priority: 3\n    },\n    pt: {\n        name: \"Portuguese\",\n        nativeName: \"Portugu\\xeas\",\n        direction: \"ltr\",\n        region: \"Brazil & Portuguese-speaking countries\",\n        population: \"260 million\",\n        flag: \"\\uD83C\\uDDE7\\uD83C\\uDDF7\",\n        hreflang: \"pt-BR\",\n        locale: \"pt-BR\",\n        currency: \"BRL\",\n        fontFamily: \"latin\",\n        expansionFactor: 1.20,\n        priority: 4\n    },\n    hi: {\n        name: \"Hindi\",\n        nativeName: \"हिन्दी\",\n        direction: \"ltr\",\n        region: \"Northern India\",\n        population: \"600 million\",\n        flag: \"\\uD83C\\uDDEE\\uD83C\\uDDF3\",\n        hreflang: \"hi-IN\",\n        locale: \"hi-IN\",\n        currency: \"INR\",\n        fontFamily: \"hindi\",\n        expansionFactor: 1.40,\n        priority: 5\n    },\n    ja: {\n        name: \"Japanese\",\n        nativeName: \"日本語\",\n        direction: \"ltr\",\n        region: \"Japan\",\n        population: \"125 million\",\n        flag: \"\\uD83C\\uDDEF\\uD83C\\uDDF5\",\n        hreflang: \"ja-JP\",\n        locale: \"ja-JP\",\n        currency: \"JPY\",\n        fontFamily: \"japanese\",\n        expansionFactor: 1.10,\n        priority: 6\n    }\n};\n// 默认语言\nconst defaultLocale = \"en\";\n// 语言检测配置\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_intl_server__WEBPACK_IMPORTED_MODULE_1__.getRequestConfig)(async ({ locale })=>{\n    // 验证传入的语言是否支持\n    if (!locales.includes(locale)) {\n        (0,next_navigation__WEBPACK_IMPORTED_MODULE_0__.notFound)();\n    }\n    try {\n        const messages = (await __webpack_require__(\"(middleware)/./messages lazy recursive ^\\\\.\\\\/.*\\\\.json$\")(`./${locale}.json`)).default;\n        return {\n            messages,\n            timeZone: getTimeZoneForLocale(locale),\n            now: new Date()\n        };\n    } catch (error) {\n        console.error(`Failed to load messages for locale: ${locale}`, error);\n        (0,next_navigation__WEBPACK_IMPORTED_MODULE_0__.notFound)();\n    }\n}));\n// 根据语言获取时区\nfunction getTimeZoneForLocale(locale) {\n    const timezoneMap = {\n        en: \"America/New_York\",\n        zh: \"Asia/Shanghai\",\n        es: \"Europe/Madrid\",\n        pt: \"America/Sao_Paulo\",\n        hi: \"Asia/Kolkata\",\n        ja: \"Asia/Tokyo\"\n    };\n    return timezoneMap[locale] || \"UTC\";\n}\n// 语言路径配置\nconst pathnames = {\n    \"/\": \"/\",\n    \"/blog\": {\n        en: \"/blog\",\n        zh: \"/blog\",\n        es: \"/blog\",\n        pt: \"/blog\",\n        hi: \"/blog\",\n        ja: \"/blog\"\n    },\n    \"/tarot\": {\n        en: \"/tarot\",\n        zh: \"/tarot\",\n        es: \"/tarot\",\n        pt: \"/tarot\",\n        hi: \"/tarot\",\n        ja: \"/tarot\"\n    },\n    \"/astrology\": {\n        en: \"/astrology\",\n        zh: \"/astrology\",\n        es: \"/astrology\",\n        pt: \"/astrology\",\n        hi: \"/astrology\",\n        ja: \"/astrology\"\n    },\n    \"/numerology\": {\n        en: \"/numerology\",\n        zh: \"/numerology\",\n        es: \"/numerology\",\n        pt: \"/numerology\",\n        hi: \"/numerology\",\n        ja: \"/numerology\"\n    }\n};\n// 语言特定的格式化配置\nconst formatConfig = {\n    en: {\n        dateFormat: \"MM/dd/yyyy\",\n        timeFormat: \"12h\",\n        currency: \"USD\",\n        numberFormat: \"en-US\"\n    },\n    zh: {\n        dateFormat: \"yyyy年MM月dd日\",\n        timeFormat: \"24h\",\n        currency: \"CNY\",\n        numberFormat: \"zh-CN\"\n    },\n    es: {\n        dateFormat: \"dd/MM/yyyy\",\n        timeFormat: \"24h\",\n        currency: \"EUR\",\n        numberFormat: \"es-ES\"\n    },\n    pt: {\n        dateFormat: \"dd/MM/yyyy\",\n        timeFormat: \"24h\",\n        currency: \"BRL\",\n        numberFormat: \"pt-BR\"\n    },\n    hi: {\n        dateFormat: \"dd/MM/yyyy\",\n        timeFormat: \"12h\",\n        currency: \"INR\",\n        numberFormat: \"hi-IN\"\n    },\n    ja: {\n        dateFormat: \"yyyy年MM月dd日\",\n        timeFormat: \"24h\",\n        currency: \"JPY\",\n        numberFormat: \"ja-JP\"\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./src/i18n.ts\n");

/***/ })

});
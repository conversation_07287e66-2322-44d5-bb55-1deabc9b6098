@tailwind base;
@tailwind components;
@tailwind utilities;

/* 导入字体 */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Cinzel:wght@400;500;600&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+JP:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap');

@layer base {
  :root {
    /* 神秘紫色系变量 - 根据设计规范更新 */
    --mystical-50: 250 247 255;   /* 极浅紫，背景色 */
    --mystical-100: 243 236 255;  /* 浅紫，卡片背景 */
    --mystical-200: 233 216 255;  /* 淡紫，悬停状态 */
    --mystical-300: 216 185 255;  /* 中浅紫，边框 */
    --mystical-400: 192 132 252;  /* 中紫，次要按钮 */
    --mystical-500: 168 85 247;   /* 标准紫，主按钮 */
    --mystical-600: 147 51 234;   /* 深紫，按钮悬停 */
    --mystical-700: 124 58 237;   /* 更深紫，激活状态 */
    --mystical-800: 107 33 168;   /* 很深紫，文字 */
    --mystical-900: 88 28 135;    /* 最深紫，标题 */

    /* 黄金色系变量 */
    --gold-50: 255 251 235;   /* 极浅金 */
    --gold-100: 254 243 199;  /* 浅金 */
    --gold-200: 253 230 138;  /* 淡金 */
    --gold-300: 252 211 77;   /* 中浅金 */
    --gold-400: 251 191 36;   /* 中金 */
    --gold-500: 245 158 11;   /* 标准金，强调色 */
    --gold-600: 217 119 6;    /* 深金 */
    --gold-700: 180 83 9;     /* 更深金 */
    --gold-800: 146 64 14;    /* 很深金 */
    --gold-900: 120 53 15;    /* 最深金 */

    /* 深色系变量 */
    --dark-50: 248 250 252;   /* 极浅灰 */
    --dark-100: 241 245 249;  /* 浅灰 */
    --dark-200: 226 232 240;  /* 淡灰 */
    --dark-300: 203 213 225;  /* 中浅灰 */
    --dark-400: 148 163 184;  /* 中灰 */
    --dark-500: 100 116 139;  /* 标准灰 */
    --dark-600: 71 85 105;    /* 深灰 */
    --dark-700: 51 65 85;     /* 更深灰 */
    --dark-800: 30 41 59;     /* 很深灰，深色模式背景 */
    --dark-900: 15 23 42;     /* 最深灰，深色模式主背景 */

    /* 星座色彩系统变量 */
    --zodiac-fire: 255 107 107;    /* 火象星座 - 红色系 */
    --zodiac-earth: 81 207 102;    /* 土象星座 - 绿色系 */
    --zodiac-air: 116 192 252;     /* 风象星座 - 蓝色系 */
    --zodiac-water: 132 94 247;    /* 水象星座 - 紫色系 */

    /* 默认浅色主题变量 - 确保默认为白色背景 */
    --background: 255 255 255;
    --background-secondary: 248 250 252;
    --background-tertiary: 241 245 249;

    --foreground: 15 23 42;
    --foreground-secondary: 51 65 85;
    --foreground-muted: 100 116 139;

    --border: 226 232 240;
    --border-secondary: 203 213 225;

    --card: 255 255 255;
    --card-secondary: 248 250 252;
  }

  .dark {
    /* 深色主题变量 - 神秘深色背景 */
    --background: 15 23 42;        /* 最深灰，主背景 */
    --background-secondary: 30 41 59;    /* 很深灰，卡片背景 */
    --background-tertiary: 51 65 85;     /* 深灰，悬停背景 */

    --foreground: 255 255 255;
    --foreground-secondary: 226 232 240;
    --foreground-muted: 148 163 184;

    --border: 71 85 105;           /* 深灰边框 */
    --border-secondary: 100 116 139;     /* 中灰边框 */

    --card: 30 41 59;              /* 卡片背景 */
    --card-secondary: 51 65 85;    /* 次要卡片背景 */
  }

  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans transition-colors duration-300;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  /* 确保主题切换的平滑过渡 */
  * {
    transition-property: background-color, border-color, color, fill, stroke;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 300ms;
  }

  /* 滚动条样式 */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-background-secondary;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-border rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-border-secondary;
  }

  /* 选择文本样式 */
  ::selection {
    @apply bg-mystical-500/20 text-mystical-900;
  }

  /* 焦点样式 */
  :focus-visible {
    @apply outline-none ring-2 ring-mystical-500 ring-offset-2 ring-offset-background;
  }
}

@layer components {
  /* 神秘主题组件样式 - 根据设计规范更新 */
  .mystical-card {
    @apply bg-gradient-to-br from-card to-card/80 border border-mystical-300/20 shadow-mystical backdrop-blur-sm;
  }

  .golden-card {
    @apply bg-gradient-to-br from-card to-gold-500/5 border border-gold-500/20 shadow-gold backdrop-blur-sm;
  }

  .glass-card {
    @apply bg-white/5 border border-white/10 backdrop-blur-md shadow-lg;
  }

  /* 渐变文本 */
  .text-mystical-gradient {
    @apply bg-gradient-to-r from-mystical-400 to-mystical-600 bg-clip-text text-transparent;
  }

  .text-golden-gradient {
    @apply bg-gradient-to-r from-gold-400 to-gold-600 bg-clip-text text-transparent;
  }

  /* 神秘按钮效果 */
  .btn-mystical {
    @apply bg-gradient-to-r from-mystical-500 to-mystical-600 text-white shadow-mystical hover:shadow-mystical-lg transition-all duration-300 hover:scale-105;
  }

  .btn-golden {
    @apply bg-gradient-to-r from-gold-500 to-gold-600 text-white shadow-gold hover:shadow-gold transition-all duration-300 hover:scale-105;
  }

  /* 动画效果 */
  .animate-float {
    animation: float 3s ease-in-out infinite;
  }

  .animate-glow {
    animation: glow 2s ease-in-out infinite alternate;
  }

  .animate-shimmer {
    animation: shimmer 2s linear infinite;
  }

  /* 星空背景 */
  .star-field {
    background-image: 
      radial-gradient(2px 2px at 20px 30px, #eee, transparent),
      radial-gradient(2px 2px at 40px 70px, rgba(255,255,255,0.8), transparent),
      radial-gradient(1px 1px at 90px 40px, #fff, transparent),
      radial-gradient(1px 1px at 130px 80px, rgba(255,255,255,0.6), transparent),
      radial-gradient(2px 2px at 160px 30px, #ddd, transparent);
    background-repeat: repeat;
    background-size: 200px 100px;
  }

  /* 响应式容器 */
  .container-responsive {
    @apply container mx-auto px-4 sm:px-6 lg:px-8;
  }

  /* 文本样式 */
  .text-balance {
    text-wrap: balance;
  }

  /* 多语言字体支持 */
  .font-chinese {
    font-family: 'Noto Sans SC', 'PingFang SC', sans-serif;
  }

  .font-japanese {
    font-family: 'Noto Sans JP', sans-serif;
  }

  .font-arabic {
    font-family: 'Noto Sans Arabic', sans-serif;
    direction: rtl;
  }

  /* 加载动画 */
  .loading-spinner {
    @apply animate-spin rounded-full border-2 border-gray-300 border-t-mystical-500;
  }

  /* 卡片悬停效果 */
  .card-hover {
    @apply transition-all duration-300 hover:shadow-lg hover:-translate-y-1 hover:scale-[1.02];
  }

  /* 神秘光效 - 根据设计规范更新 */
  .mystical-glow {
    box-shadow:
      0 0 20px rgba(168, 85, 247, 0.3),
      0 0 40px rgba(168, 85, 247, 0.2),
      0 0 60px rgba(168, 85, 247, 0.1);
  }

  .golden-glow {
    box-shadow:
      0 0 20px rgba(245, 158, 11, 0.3),
      0 0 40px rgba(245, 158, 11, 0.2),
      0 0 60px rgba(245, 158, 11, 0.1);
  }

  /* 星座主题光效 */
  .fire-glow {
    box-shadow:
      0 0 20px rgba(255, 107, 107, 0.3),
      0 0 40px rgba(255, 107, 107, 0.2);
  }

  .earth-glow {
    box-shadow:
      0 0 20px rgba(81, 207, 102, 0.3),
      0 0 40px rgba(81, 207, 102, 0.2);
  }

  .air-glow {
    box-shadow:
      0 0 20px rgba(116, 192, 252, 0.3),
      0 0 40px rgba(116, 192, 252, 0.2);
  }

  .water-glow {
    box-shadow:
      0 0 20px rgba(132, 94, 247, 0.3),
      0 0 40px rgba(132, 94, 247, 0.2);
  }
}

@layer utilities {
  /* 隐藏滚动条但保持功能 */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* 文本省略 */
  .text-ellipsis-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .text-ellipsis-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* 安全区域适配 */
  .safe-top {
    padding-top: env(safe-area-inset-top);
  }

  .safe-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }

  .safe-left {
    padding-left: env(safe-area-inset-left);
  }

  .safe-right {
    padding-right: env(safe-area-inset-right);
  }
}

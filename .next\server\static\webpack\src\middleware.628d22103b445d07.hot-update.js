"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("src/middleware",{

/***/ "(middleware)/./src/i18n.ts":
/*!*********************!*\
  !*** ./src/i18n.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   defaultLocale: () => (/* binding */ defaultLocale),\n/* harmony export */   fallbackChain: () => (/* binding */ fallbackChain),\n/* harmony export */   formatConfig: () => (/* binding */ formatConfig),\n/* harmony export */   languageConfig: () => (/* binding */ languageConfig),\n/* harmony export */   locales: () => (/* binding */ locales),\n/* harmony export */   pathnames: () => (/* binding */ pathnames),\n/* harmony export */   phase2Locales: () => (/* binding */ phase2Locales),\n/* harmony export */   rtlLocales: () => (/* binding */ rtlLocales)\n/* harmony export */ });\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/navigation */ \"(middleware)/./node_modules/next/dist/esm/api/navigation.js\");\n/* harmony import */ var next_intl_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-intl/server */ \"(middleware)/./node_modules/next-intl/dist/development/server.react-client.js\");\n\n\n// 第一阶段：核心市场语言 (立即实施)\nconst locales = [\n    \"en\",\n    \"zh\",\n    \"es\",\n    \"pt\",\n    \"hi\",\n    \"ja\"\n];\n// 第二阶段：重要区域语言 (6个月后扩展)\nconst phase2Locales = [\n    \"de\",\n    \"fr\",\n    \"it\",\n    \"ru\",\n    \"ko\",\n    \"ar\"\n];\n// 语言配置 - 包含完整的多语言元数据\nconst languageConfig = {\n    en: {\n        name: \"English\",\n        nativeName: \"English\",\n        direction: \"ltr\",\n        region: \"Global\",\n        population: \"Global market\",\n        flag: \"\\uD83C\\uDDFA\\uD83C\\uDDF8\",\n        hreflang: \"en\",\n        locale: \"en-US\",\n        currency: \"USD\",\n        fontFamily: \"latin\",\n        expansionFactor: 1.0,\n        priority: 1\n    },\n    zh: {\n        name: \"Chinese\",\n        nativeName: \"中文\",\n        direction: \"ltr\",\n        region: \"China & Chinese communities\",\n        population: \"1.4 billion\",\n        flag: \"\\uD83C\\uDDE8\\uD83C\\uDDF3\",\n        hreflang: \"zh-CN\",\n        locale: \"zh-CN\",\n        currency: \"CNY\",\n        fontFamily: \"chinese\",\n        expansionFactor: 0.8,\n        priority: 2\n    },\n    es: {\n        name: \"Spanish\",\n        nativeName: \"Espa\\xf1ol\",\n        direction: \"ltr\",\n        region: \"Spain & Latin America\",\n        population: \"500 million\",\n        flag: \"\\uD83C\\uDDEA\\uD83C\\uDDF8\",\n        hreflang: \"es\",\n        locale: \"es-ES\",\n        currency: \"EUR\",\n        fontFamily: \"latin\",\n        expansionFactor: 1.25,\n        priority: 3\n    },\n    pt: {\n        name: \"Portuguese\",\n        nativeName: \"Portugu\\xeas\",\n        direction: \"ltr\",\n        region: \"Brazil & Portuguese-speaking countries\",\n        population: \"260 million\",\n        flag: \"\\uD83C\\uDDE7\\uD83C\\uDDF7\",\n        hreflang: \"pt-BR\",\n        locale: \"pt-BR\",\n        currency: \"BRL\",\n        fontFamily: \"latin\",\n        expansionFactor: 1.20,\n        priority: 4\n    },\n    hi: {\n        name: \"Hindi\",\n        nativeName: \"हिन्दी\",\n        direction: \"ltr\",\n        region: \"Northern India\",\n        population: \"600 million\",\n        flag: \"\\uD83C\\uDDEE\\uD83C\\uDDF3\",\n        hreflang: \"hi-IN\",\n        locale: \"hi-IN\",\n        currency: \"INR\",\n        fontFamily: \"hindi\",\n        expansionFactor: 1.40,\n        priority: 5\n    },\n    ja: {\n        name: \"Japanese\",\n        nativeName: \"日本語\",\n        direction: \"ltr\",\n        region: \"Japan\",\n        population: \"125 million\",\n        flag: \"\\uD83C\\uDDEF\\uD83C\\uDDF5\",\n        hreflang: \"ja-JP\",\n        locale: \"ja-JP\",\n        currency: \"JPY\",\n        fontFamily: \"japanese\",\n        expansionFactor: 1.10,\n        priority: 6\n    }\n};\n// 默认语言\nconst defaultLocale = \"en\";\n// RTL语言配置\nconst rtlLocales = [\n    \"ar\",\n    \"he\",\n    \"fa\",\n    \"ur\"\n];\n// 语言回退链配置\nconst fallbackChain = {\n    en: [],\n    zh: [\n        \"en\"\n    ],\n    es: [\n        \"en\"\n    ],\n    pt: [\n        \"es\",\n        \"en\"\n    ],\n    hi: [\n        \"en\"\n    ],\n    ja: [\n        \"en\"\n    ]\n};\n// 语言检测配置 - 增强版本，支持回退机制\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_intl_server__WEBPACK_IMPORTED_MODULE_1__.getRequestConfig)(async ({ locale })=>{\n    // 验证传入的语言是否支持\n    if (!locales.includes(locale)) {\n        (0,next_navigation__WEBPACK_IMPORTED_MODULE_0__.notFound)();\n    }\n    try {\n        // 尝试加载主要语言文件\n        const messages = (await __webpack_require__(\"(middleware)/./messages lazy recursive ^\\\\.\\\\/.*\\\\.json$\")(`./${locale}.json`)).default;\n        // 如果有回退语言，合并消息\n        const fallbacks = fallbackChain[locale] || [];\n        let mergedMessages = messages;\n        for (const fallbackLocale of fallbacks){\n            try {\n                const fallbackMessages = (await __webpack_require__(\"(middleware)/./messages lazy recursive ^\\\\.\\\\/.*\\\\.json$\")(`./${fallbackLocale}.json`)).default;\n                mergedMessages = {\n                    ...fallbackMessages,\n                    ...mergedMessages\n                };\n            } catch (fallbackError) {\n                console.warn(`Failed to load fallback messages for locale: ${fallbackLocale}`, fallbackError);\n            }\n        }\n        return {\n            messages: mergedMessages,\n            timeZone: getTimeZoneForLocale(locale),\n            now: new Date(),\n            formats: getFormatsForLocale(locale)\n        };\n    } catch (error) {\n        console.error(`Failed to load messages for locale: ${locale}`, error);\n        // 尝试回退到默认语言\n        if (locale !== defaultLocale) {\n            try {\n                const defaultMessages = (await __webpack_require__(\"(middleware)/./messages lazy recursive ^\\\\.\\\\/.*\\\\.json$\")(`./${defaultLocale}.json`)).default;\n                return {\n                    messages: defaultMessages,\n                    timeZone: getTimeZoneForLocale(defaultLocale),\n                    now: new Date(),\n                    formats: getFormatsForLocale(defaultLocale)\n                };\n            } catch (defaultError) {\n                console.error(`Failed to load default locale messages`, defaultError);\n            }\n        }\n        (0,next_navigation__WEBPACK_IMPORTED_MODULE_0__.notFound)();\n    }\n}));\n// 根据语言获取时区\nfunction getTimeZoneForLocale(locale) {\n    const timezoneMap = {\n        en: \"America/New_York\",\n        zh: \"Asia/Shanghai\",\n        es: \"Europe/Madrid\",\n        pt: \"America/Sao_Paulo\",\n        hi: \"Asia/Kolkata\",\n        ja: \"Asia/Tokyo\"\n    };\n    return timezoneMap[locale] || \"UTC\";\n}\n// 根据语言获取格式化配置\nfunction getFormatsForLocale(locale) {\n    const formatMap = {\n        en: {\n            dateTime: {\n                short: {\n                    day: \"numeric\",\n                    month: \"short\",\n                    year: \"numeric\"\n                },\n                long: {\n                    day: \"numeric\",\n                    month: \"long\",\n                    year: \"numeric\",\n                    weekday: \"long\"\n                }\n            },\n            number: {\n                currency: {\n                    style: \"currency\",\n                    currency: \"USD\"\n                }\n            }\n        },\n        zh: {\n            dateTime: {\n                short: {\n                    day: \"numeric\",\n                    month: \"short\",\n                    year: \"numeric\"\n                },\n                long: {\n                    day: \"numeric\",\n                    month: \"long\",\n                    year: \"numeric\",\n                    weekday: \"long\"\n                }\n            },\n            number: {\n                currency: {\n                    style: \"currency\",\n                    currency: \"CNY\"\n                }\n            }\n        },\n        es: {\n            dateTime: {\n                short: {\n                    day: \"numeric\",\n                    month: \"short\",\n                    year: \"numeric\"\n                },\n                long: {\n                    day: \"numeric\",\n                    month: \"long\",\n                    year: \"numeric\",\n                    weekday: \"long\"\n                }\n            },\n            number: {\n                currency: {\n                    style: \"currency\",\n                    currency: \"EUR\"\n                }\n            }\n        },\n        pt: {\n            dateTime: {\n                short: {\n                    day: \"numeric\",\n                    month: \"short\",\n                    year: \"numeric\"\n                },\n                long: {\n                    day: \"numeric\",\n                    month: \"long\",\n                    year: \"numeric\",\n                    weekday: \"long\"\n                }\n            },\n            number: {\n                currency: {\n                    style: \"currency\",\n                    currency: \"BRL\"\n                }\n            }\n        },\n        hi: {\n            dateTime: {\n                short: {\n                    day: \"numeric\",\n                    month: \"short\",\n                    year: \"numeric\"\n                },\n                long: {\n                    day: \"numeric\",\n                    month: \"long\",\n                    year: \"numeric\",\n                    weekday: \"long\"\n                }\n            },\n            number: {\n                currency: {\n                    style: \"currency\",\n                    currency: \"INR\"\n                }\n            }\n        },\n        ja: {\n            dateTime: {\n                short: {\n                    day: \"numeric\",\n                    month: \"short\",\n                    year: \"numeric\"\n                },\n                long: {\n                    day: \"numeric\",\n                    month: \"long\",\n                    year: \"numeric\",\n                    weekday: \"long\"\n                }\n            },\n            number: {\n                currency: {\n                    style: \"currency\",\n                    currency: \"JPY\"\n                }\n            }\n        }\n    };\n    return formatMap[locale] || formatMap[defaultLocale];\n}\n// 语言路径配置\nconst pathnames = {\n    \"/\": \"/\",\n    \"/blog\": {\n        en: \"/blog\",\n        zh: \"/blog\",\n        es: \"/blog\",\n        pt: \"/blog\",\n        hi: \"/blog\",\n        ja: \"/blog\"\n    },\n    \"/tarot\": {\n        en: \"/tarot\",\n        zh: \"/tarot\",\n        es: \"/tarot\",\n        pt: \"/tarot\",\n        hi: \"/tarot\",\n        ja: \"/tarot\"\n    },\n    \"/astrology\": {\n        en: \"/astrology\",\n        zh: \"/astrology\",\n        es: \"/astrology\",\n        pt: \"/astrology\",\n        hi: \"/astrology\",\n        ja: \"/astrology\"\n    },\n    \"/numerology\": {\n        en: \"/numerology\",\n        zh: \"/numerology\",\n        es: \"/numerology\",\n        pt: \"/numerology\",\n        hi: \"/numerology\",\n        ja: \"/numerology\"\n    }\n};\n// 语言特定的格式化配置\nconst formatConfig = {\n    en: {\n        dateFormat: \"MM/dd/yyyy\",\n        timeFormat: \"12h\",\n        currency: \"USD\",\n        numberFormat: \"en-US\"\n    },\n    zh: {\n        dateFormat: \"yyyy年MM月dd日\",\n        timeFormat: \"24h\",\n        currency: \"CNY\",\n        numberFormat: \"zh-CN\"\n    },\n    es: {\n        dateFormat: \"dd/MM/yyyy\",\n        timeFormat: \"24h\",\n        currency: \"EUR\",\n        numberFormat: \"es-ES\"\n    },\n    pt: {\n        dateFormat: \"dd/MM/yyyy\",\n        timeFormat: \"24h\",\n        currency: \"BRL\",\n        numberFormat: \"pt-BR\"\n    },\n    hi: {\n        dateFormat: \"dd/MM/yyyy\",\n        timeFormat: \"12h\",\n        currency: \"INR\",\n        numberFormat: \"hi-IN\"\n    },\n    ja: {\n        dateFormat: \"yyyy年MM月dd日\",\n        timeFormat: \"24h\",\n        currency: \"JPY\",\n        numberFormat: \"ja-JP\"\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./src/i18n.ts\n");

/***/ })

});
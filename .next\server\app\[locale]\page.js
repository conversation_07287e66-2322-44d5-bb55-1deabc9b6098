/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/[locale]/page";
exports.ids = ["app/[locale]/page"];
exports.modules = {

/***/ "(rsc)/./messages lazy recursive ^\\.\\/.*\\.json$":
/*!********************************************************!*\
  !*** ./messages/ lazy ^\.\/.*\.json$ namespace object ***!
  \********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var map = {
	"./en.json": [
		"(rsc)/./messages/en.json",
		"_rsc_messages_en_json"
	],
	"./es.json": [
		"(rsc)/./messages/es.json",
		"_rsc_messages_es_json"
	],
	"./hi.json": [
		"(rsc)/./messages/hi.json",
		"_rsc_messages_hi_json"
	],
	"./ja.json": [
		"(rsc)/./messages/ja.json",
		"_rsc_messages_ja_json"
	],
	"./pt.json": [
		"(rsc)/./messages/pt.json",
		"_rsc_messages_pt_json"
	],
	"./zh.json": [
		"(rsc)/./messages/zh.json",
		"_rsc_messages_zh_json"
	]
};
function webpackAsyncContext(req) {
	if(!__webpack_require__.o(map, req)) {
		return Promise.resolve().then(() => {
			var e = new Error("Cannot find module '" + req + "'");
			e.code = 'MODULE_NOT_FOUND';
			throw e;
		});
	}

	var ids = map[req], id = ids[0];
	return __webpack_require__.e(ids[1]).then(() => {
		return __webpack_require__.t(id, 3 | 16);
	});
}
webpackAsyncContext.keys = () => (Object.keys(map));
webpackAsyncContext.id = "(rsc)/./messages lazy recursive ^\\.\\/.*\\.json$";
module.exports = webpackAsyncContext;

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F%5Blocale%5D%2Fpage&page=%2F%5Blocale%5D%2Fpage&appPaths=%2F%5Blocale%5D%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Fpage.tsx&appDir=E%3A%5Ctarot-seo%5Csrc%5Capp&pageExtensions=ts&pageExtensions=tsx&pageExtensions=js&pageExtensions=jsx&pageExtensions=md&pageExtensions=mdx&rootDir=E%3A%5Ctarot-seo&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F%5Blocale%5D%2Fpage&page=%2F%5Blocale%5D%2Fpage&appPaths=%2F%5Blocale%5D%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Fpage.tsx&appDir=E%3A%5Ctarot-seo%5Csrc%5Capp&pageExtensions=ts&pageExtensions=tsx&pageExtensions=js&pageExtensions=jsx&pageExtensions=md&pageExtensions=mdx&rootDir=E%3A%5Ctarot-seo&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        '[locale]',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/[locale]/page.tsx */ \"(rsc)/./src/app/[locale]/page.tsx\")), \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/[locale]/layout.tsx */ \"(rsc)/./src/app/[locale]/layout.tsx\")), \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\layout.tsx\"],\n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"E:\\\\tarot-seo\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/[locale]/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/[locale]/page\",\n        pathname: \"/[locale]\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F%5Blocale%5D%2Fpage&page=%2F%5Blocale%5D%2Fpage&appPaths=%2F%5Blocale%5D%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Fpage.tsx&appDir=E%3A%5Ctarot-seo%5Csrc%5Capp&pageExtensions=ts&pageExtensions=tsx&pageExtensions=js&pageExtensions=jsx&pageExtensions=md&pageExtensions=mdx&rootDir=E%3A%5Ctarot-seo&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Noto_Sans_SC%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-noto-sans-sc%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22notoSansSC%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Noto_Sans_JP%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-noto-sans-jp%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22notoSansJP%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Noto_Sans%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%2C%5C%22devanagari%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-noto-sans%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22notoSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Csrc%5C%5Cstyles%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Noto_Sans_SC%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-noto-sans-sc%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22notoSansSC%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Noto_Sans_JP%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-noto-sans-jp%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22notoSansJP%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Noto_Sans%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%2C%5C%22devanagari%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-noto-sans%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22notoSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Csrc%5C%5Cstyles%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5Cheader.tsx%22%2C%22ids%22%3A%5B%22Header%22%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5Cheader.tsx%22%2C%22ids%22%3A%5B%22Header%22%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js */ \"(ssr)/./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/header.tsx */ \"(ssr)/./src/components/layout/header.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUN0YXJvdC1zZW8lNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0LWludGwlNUMlNUNkaXN0JTVDJTVDZXNtJTVDJTVDc2hhcmVkJTVDJTVDTmV4dEludGxDbGllbnRQcm92aWRlci5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRSUzQSU1QyU1Q3Rhcm90LXNlbyU1QyU1Q3NyYyU1QyU1Q2NvbXBvbmVudHMlNUMlNUNsYXlvdXQlNUMlNUNoZWFkZXIudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIySGVhZGVyJTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnUEFBc0o7QUFDdEo7QUFDQSxnTEFBbUgiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9teXN0aWNhbC13ZWJzaXRlLz9hOTMzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZGVmYXVsdFwiXSAqLyBcIkU6XFxcXHRhcm90LXNlb1xcXFxub2RlX21vZHVsZXNcXFxcbmV4dC1pbnRsXFxcXGRpc3RcXFxcZXNtXFxcXHNoYXJlZFxcXFxOZXh0SW50bENsaWVudFByb3ZpZGVyLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJIZWFkZXJcIl0gKi8gXCJFOlxcXFx0YXJvdC1zZW9cXFxcc3JjXFxcXGNvbXBvbmVudHNcXFxcbGF5b3V0XFxcXGhlYWRlci50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5Cheader.tsx%22%2C%22ids%22%3A%5B%22Header%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js */ \"(ssr)/./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUN0YXJvdC1zZW8lNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0LWludGwlNUMlNUNkaXN0JTVDJTVDZXNtJTVDJTVDc2hhcmVkJTVDJTVDTmV4dEludGxDbGllbnRQcm92aWRlci5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGdQQUFzSiIsInNvdXJjZXMiOlsid2VicGFjazovL215c3RpY2FsLXdlYnNpdGUvPzRhNGIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiRTpcXFxcdGFyb3Qtc2VvXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0LWludGxcXFxcZGlzdFxcXFxlc21cXFxcc2hhcmVkXFxcXE5leHRJbnRsQ2xpZW50UHJvdmlkZXIuanNcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/header.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/header.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Header: () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next-intl */ \"(ssr)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Menu,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Menu,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Menu,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_theme_toggle__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/theme-toggle */ \"(ssr)/./src/components/ui/theme-toggle.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Header auto */ \n\n\n\n\n\n\n\nfunction Header() {\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_6__.useTranslations)(\"navigation\");\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeDropdown, setActiveDropdown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const navigation = [\n        {\n            label: t(\"home\"),\n            href: \"/\"\n        },\n        {\n            label: t(\"tarot\"),\n            href: \"/tarot\",\n            children: [\n                {\n                    label: \"Tarot Reading\",\n                    href: \"/tarot/test\"\n                },\n                {\n                    label: \"Tarot Cards\",\n                    href: \"/tarot/cards\"\n                },\n                {\n                    label: \"Tarot Guide\",\n                    href: \"/tarot/guide\"\n                },\n                {\n                    label: \"Tarot History\",\n                    href: \"/tarot/history\"\n                }\n            ]\n        },\n        {\n            label: t(\"astrology\"),\n            href: \"/astrology\",\n            children: [\n                {\n                    label: \"Astrology Test\",\n                    href: \"/astrology/test\"\n                },\n                {\n                    label: \"Zodiac Signs\",\n                    href: \"/astrology/signs\"\n                },\n                {\n                    label: \"Compatibility\",\n                    href: \"/astrology/compatibility\"\n                },\n                {\n                    label: \"Horoscope\",\n                    href: \"/astrology/horoscope\"\n                }\n            ]\n        },\n        {\n            label: t(\"numerology\"),\n            href: \"/numerology\",\n            children: [\n                {\n                    label: \"Numerology Test\",\n                    href: \"/numerology/test\"\n                },\n                {\n                    label: \"Number Calculator\",\n                    href: \"/numerology/calculator\"\n                },\n                {\n                    label: \"Number Meanings\",\n                    href: \"/numerology/meanings\"\n                }\n            ]\n        },\n        {\n            label: t(\"blog\"),\n            href: \"/blog\"\n        }\n    ];\n    const toggleMenu = ()=>{\n        setIsMenuOpen(!isMenuOpen);\n        setActiveDropdown(null);\n    };\n    const toggleDropdown = (label)=>{\n        setActiveDropdown(activeDropdown === label ? null : label);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"sticky top-0 z-50 w-full border-b border-border/40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex h-16 items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            href: \"/\",\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-8 w-8 rounded-full bg-mystical-gradient flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white font-bold text-sm\",\n                                        children: \"M\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                        lineNumber: 80,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                    lineNumber: 79,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-mystical text-xl font-bold bg-mystical-gradient bg-clip-text text-transparent\",\n                                    children: \"Mystical\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                            lineNumber: 78,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"hidden md:flex items-center space-x-8\",\n                            children: navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative group\",\n                                    children: item.children ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"flex items-center space-x-1 text-foreground/80 hover:text-foreground transition-colors\",\n                                                onMouseEnter: ()=>setActiveDropdown(item.label),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: item.label\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                                        lineNumber: 97,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                                        lineNumber: 98,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                                lineNumber: 93,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"absolute top-full left-0 mt-2 w-48 bg-card border border-border rounded-lg shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200\", \"before:absolute before:-top-2 before:left-0 before:w-full before:h-2\"),\n                                                onMouseLeave: ()=>setActiveDropdown(null),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"py-2\",\n                                                    children: item.children.map((child)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                            href: child.href,\n                                                            className: \"block px-4 py-2 text-sm text-foreground/80 hover:text-foreground hover:bg-accent/50 transition-colors\",\n                                                            children: child.label\n                                                        }, child.href, false, {\n                                                            fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                                            lineNumber: 111,\n                                                            columnNumber: 27\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                                    lineNumber: 109,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                                lineNumber: 102,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: item.href,\n                                        className: \"text-foreground/80 hover:text-foreground transition-colors\",\n                                        children: item.label\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                        lineNumber: 123,\n                                        columnNumber: 19\n                                    }, this)\n                                }, item.label, false, {\n                                    fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_theme_toggle__WEBPACK_IMPORTED_MODULE_4__.ThemeToggle, {}, void 0, false, {\n                                    fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"mystical\",\n                                    size: \"sm\",\n                                    children: \"Start Free Test\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                    lineNumber: 137,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                            lineNumber: 135,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"md:hidden p-2\",\n                            onClick: toggleMenu,\n                            \"aria-label\": \"Toggle menu\",\n                            children: isMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"h-6 w-6\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"h-6 w-6\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                            lineNumber: 143,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                    lineNumber: 76,\n                    columnNumber: 9\n                }, this),\n                isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"md:hidden border-t border-border/40 py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"space-y-2\",\n                        children: [\n                            navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: item.children ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"flex items-center justify-between w-full px-4 py-2 text-left text-foreground/80 hover:text-foreground transition-colors\",\n                                                onClick: ()=>toggleDropdown(item.label),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: item.label\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                                        lineNumber: 168,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"h-4 w-4 transition-transform\", activeDropdown === item.label && \"rotate-180\")\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                                        lineNumber: 169,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                                lineNumber: 164,\n                                                columnNumber: 23\n                                            }, this),\n                                            activeDropdown === item.label && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"pl-4 space-y-1\",\n                                                children: item.children.map((child)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        href: child.href,\n                                                        className: \"block px-4 py-2 text-sm text-foreground/60 hover:text-foreground transition-colors\",\n                                                        onClick: ()=>setIsMenuOpen(false),\n                                                        children: child.label\n                                                    }, child.href, false, {\n                                                        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                                        lineNumber: 180,\n                                                        columnNumber: 29\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                                lineNumber: 178,\n                                                columnNumber: 25\n                                            }, this)\n                                        ]\n                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: item.href,\n                                        className: \"block px-4 py-2 text-foreground/80 hover:text-foreground transition-colors\",\n                                        onClick: ()=>setIsMenuOpen(false),\n                                        children: item.label\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 21\n                                    }, this)\n                                }, item.label, false, {\n                                    fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 17\n                                }, this)),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-4 pt-4 space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_theme_toggle__WEBPACK_IMPORTED_MODULE_4__.ThemeToggle, {}, void 0, false, {\n                                            fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                            lineNumber: 206,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"mystical\",\n                                        size: \"sm\",\n                                        className: \"w-full\",\n                                        children: \"Start Free Test\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                    lineNumber: 158,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\header.tsx\",\n            lineNumber: 75,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\header.tsx\",\n        lineNumber: 74,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9sYXlvdXQvaGVhZGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFaUM7QUFDSjtBQUNlO0FBQ1E7QUFFSjtBQUNXO0FBRTFCO0FBUTFCLFNBQVNTO0lBQ2QsTUFBTUMsSUFBSVIsMERBQWVBLENBQUM7SUFDMUIsTUFBTSxDQUFDUyxZQUFZQyxjQUFjLEdBQUdaLCtDQUFRQSxDQUFDO0lBQzdDLE1BQU0sQ0FBQ2EsZ0JBQWdCQyxrQkFBa0IsR0FBR2QsK0NBQVFBLENBQWdCO0lBRXBFLE1BQU1lLGFBQStCO1FBQ25DO1lBQ0VDLE9BQU9OLEVBQUU7WUFDVE8sTUFBTTtRQUNSO1FBQ0E7WUFDRUQsT0FBT04sRUFBRTtZQUNUTyxNQUFNO1lBQ05DLFVBQVU7Z0JBQ1I7b0JBQUVGLE9BQU87b0JBQWlCQyxNQUFNO2dCQUFjO2dCQUM5QztvQkFBRUQsT0FBTztvQkFBZUMsTUFBTTtnQkFBZTtnQkFDN0M7b0JBQUVELE9BQU87b0JBQWVDLE1BQU07Z0JBQWU7Z0JBQzdDO29CQUFFRCxPQUFPO29CQUFpQkMsTUFBTTtnQkFBaUI7YUFDbEQ7UUFDSDtRQUNBO1lBQ0VELE9BQU9OLEVBQUU7WUFDVE8sTUFBTTtZQUNOQyxVQUFVO2dCQUNSO29CQUFFRixPQUFPO29CQUFrQkMsTUFBTTtnQkFBa0I7Z0JBQ25EO29CQUFFRCxPQUFPO29CQUFnQkMsTUFBTTtnQkFBbUI7Z0JBQ2xEO29CQUFFRCxPQUFPO29CQUFpQkMsTUFBTTtnQkFBMkI7Z0JBQzNEO29CQUFFRCxPQUFPO29CQUFhQyxNQUFNO2dCQUF1QjthQUNwRDtRQUNIO1FBQ0E7WUFDRUQsT0FBT04sRUFBRTtZQUNUTyxNQUFNO1lBQ05DLFVBQVU7Z0JBQ1I7b0JBQUVGLE9BQU87b0JBQW1CQyxNQUFNO2dCQUFtQjtnQkFDckQ7b0JBQUVELE9BQU87b0JBQXFCQyxNQUFNO2dCQUF5QjtnQkFDN0Q7b0JBQUVELE9BQU87b0JBQW1CQyxNQUFNO2dCQUF1QjthQUMxRDtRQUNIO1FBQ0E7WUFDRUQsT0FBT04sRUFBRTtZQUNUTyxNQUFNO1FBQ1I7S0FDRDtJQUVELE1BQU1FLGFBQWE7UUFDakJQLGNBQWMsQ0FBQ0Q7UUFDZkcsa0JBQWtCO0lBQ3BCO0lBRUEsTUFBTU0saUJBQWlCLENBQUNKO1FBQ3RCRixrQkFBa0JELG1CQUFtQkcsUUFBUSxPQUFPQTtJQUN0RDtJQUVBLHFCQUNFLDhEQUFDSztRQUFPQyxXQUFVO2tCQUNoQiw0RUFBQ0M7WUFBSUQsV0FBVTs7OEJBQ2IsOERBQUNDO29CQUFJRCxXQUFVOztzQ0FFYiw4REFBQ3JCLGlEQUFJQTs0QkFBQ2dCLE1BQUs7NEJBQUlLLFdBQVU7OzhDQUN2Qiw4REFBQ0M7b0NBQUlELFdBQVU7OENBQ2IsNEVBQUNFO3dDQUFLRixXQUFVO2tEQUErQjs7Ozs7Ozs7Ozs7OENBRWpELDhEQUFDRTtvQ0FBS0YsV0FBVTs4Q0FBcUY7Ozs7Ozs7Ozs7OztzQ0FNdkcsOERBQUNHOzRCQUFJSCxXQUFVO3NDQUNaUCxXQUFXVyxHQUFHLENBQUMsQ0FBQ0MscUJBQ2YsOERBQUNKO29DQUFxQkQsV0FBVTs4Q0FDN0JLLEtBQUtULFFBQVEsaUJBQ1o7OzBEQUNFLDhEQUFDVTtnREFDQ04sV0FBVTtnREFDVk8sY0FBYyxJQUFNZixrQkFBa0JhLEtBQUtYLEtBQUs7O2tFQUVoRCw4REFBQ1E7a0VBQU1HLEtBQUtYLEtBQUs7Ozs7OztrRUFDakIsOERBQUNYLDhGQUFXQTt3REFBQ2lCLFdBQVU7Ozs7Ozs7Ozs7OzswREFJekIsOERBQUNDO2dEQUNDRCxXQUFXZCw4Q0FBRUEsQ0FDWCxvTEFDQTtnREFFRnNCLGNBQWMsSUFBTWhCLGtCQUFrQjswREFFdEMsNEVBQUNTO29EQUFJRCxXQUFVOzhEQUNaSyxLQUFLVCxRQUFRLENBQUNRLEdBQUcsQ0FBQyxDQUFDSyxzQkFDbEIsOERBQUM5QixpREFBSUE7NERBRUhnQixNQUFNYyxNQUFNZCxJQUFJOzREQUNoQkssV0FBVTtzRUFFVFMsTUFBTWYsS0FBSzsyREFKUGUsTUFBTWQsSUFBSTs7Ozs7Ozs7Ozs7Ozs7OztxRUFXekIsOERBQUNoQixpREFBSUE7d0NBQ0hnQixNQUFNVSxLQUFLVixJQUFJO3dDQUNmSyxXQUFVO2tEQUVUSyxLQUFLWCxLQUFLOzs7Ozs7bUNBckNQVyxLQUFLWCxLQUFLOzs7Ozs7Ozs7O3NDQTZDeEIsOERBQUNPOzRCQUFJRCxXQUFVOzs4Q0FDYiw4REFBQ2Ysb0VBQVdBOzs7Ozs4Q0FDWiw4REFBQ0QseURBQU1BO29DQUFDMEIsU0FBUTtvQ0FBV0MsTUFBSzs4Q0FBSzs7Ozs7Ozs7Ozs7O3NDQU12Qyw4REFBQ0w7NEJBQ0NOLFdBQVU7NEJBQ1ZZLFNBQVNmOzRCQUNUZ0IsY0FBVztzQ0FFVnhCLDJCQUNDLDhEQUFDUCw4RkFBQ0E7Z0NBQUNrQixXQUFVOzs7OztxREFFYiw4REFBQ25CLDhGQUFJQTtnQ0FBQ21CLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7O2dCQU1yQlgsNEJBQ0MsOERBQUNZO29CQUFJRCxXQUFVOzhCQUNiLDRFQUFDRzt3QkFBSUgsV0FBVTs7NEJBQ1pQLFdBQVdXLEdBQUcsQ0FBQyxDQUFDQyxxQkFDZiw4REFBQ0o7OENBQ0VJLEtBQUtULFFBQVEsaUJBQ1o7OzBEQUNFLDhEQUFDVTtnREFDQ04sV0FBVTtnREFDVlksU0FBUyxJQUFNZCxlQUFlTyxLQUFLWCxLQUFLOztrRUFFeEMsOERBQUNRO2tFQUFNRyxLQUFLWCxLQUFLOzs7Ozs7a0VBQ2pCLDhEQUFDWCw4RkFBV0E7d0RBQ1ZpQixXQUFXZCw4Q0FBRUEsQ0FDWCxnQ0FDQUssbUJBQW1CYyxLQUFLWCxLQUFLLElBQUk7Ozs7Ozs7Ozs7Ozs0Q0FLdENILG1CQUFtQmMsS0FBS1gsS0FBSyxrQkFDNUIsOERBQUNPO2dEQUFJRCxXQUFVOzBEQUNaSyxLQUFLVCxRQUFRLENBQUNRLEdBQUcsQ0FBQyxDQUFDSyxzQkFDbEIsOERBQUM5QixpREFBSUE7d0RBRUhnQixNQUFNYyxNQUFNZCxJQUFJO3dEQUNoQkssV0FBVTt3REFDVlksU0FBUyxJQUFNdEIsY0FBYztrRUFFNUJtQixNQUFNZixLQUFLO3VEQUxQZSxNQUFNZCxJQUFJOzs7Ozs7Ozs7OztxRUFZekIsOERBQUNoQixpREFBSUE7d0NBQ0hnQixNQUFNVSxLQUFLVixJQUFJO3dDQUNmSyxXQUFVO3dDQUNWWSxTQUFTLElBQU10QixjQUFjO2tEQUU1QmUsS0FBS1gsS0FBSzs7Ozs7O21DQXJDUFcsS0FBS1gsS0FBSzs7Ozs7MENBMkN0Qiw4REFBQ087Z0NBQUlELFdBQVU7O2tEQUNiLDhEQUFDQzt3Q0FBSUQsV0FBVTtrREFDYiw0RUFBQ2Ysb0VBQVdBOzs7Ozs7Ozs7O2tEQUVkLDhEQUFDRCx5REFBTUE7d0NBQUMwQixTQUFRO3dDQUFXQyxNQUFLO3dDQUFLWCxXQUFVO2tEQUFTOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBVXhFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbXlzdGljYWwtd2Vic2l0ZS8uL3NyYy9jb21wb25lbnRzL2xheW91dC9oZWFkZXIudHN4PzRhYzIiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCBMaW5rIGZyb20gJ25leHQvbGluayc7XG5pbXBvcnQgeyB1c2VUcmFuc2xhdGlvbnMgfSBmcm9tICduZXh0LWludGwnO1xuaW1wb3J0IHsgTWVudSwgWCwgQ2hldnJvbkRvd24gfSBmcm9tICdsdWNpZGUtcmVhY3QnO1xuXG5pbXBvcnQgeyBCdXR0b24gfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvYnV0dG9uJztcbmltcG9ydCB7IFRoZW1lVG9nZ2xlIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL3RoZW1lLXRvZ2dsZSc7XG5pbXBvcnQgeyBMYW5ndWFnZVN3aXRjaGVyIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2xhbmd1YWdlLXN3aXRjaGVyJztcbmltcG9ydCB7IGNuIH0gZnJvbSAnQC9saWIvdXRpbHMnO1xuXG5pbnRlcmZhY2UgTmF2aWdhdGlvbkl0ZW0ge1xuICBsYWJlbDogc3RyaW5nO1xuICBocmVmOiBzdHJpbmc7XG4gIGNoaWxkcmVuPzogTmF2aWdhdGlvbkl0ZW1bXTtcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIEhlYWRlcigpIHtcbiAgY29uc3QgdCA9IHVzZVRyYW5zbGF0aW9ucygnbmF2aWdhdGlvbicpO1xuICBjb25zdCBbaXNNZW51T3Blbiwgc2V0SXNNZW51T3Blbl0gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFthY3RpdmVEcm9wZG93biwgc2V0QWN0aXZlRHJvcGRvd25dID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbCk7XG5cbiAgY29uc3QgbmF2aWdhdGlvbjogTmF2aWdhdGlvbkl0ZW1bXSA9IFtcbiAgICB7XG4gICAgICBsYWJlbDogdCgnaG9tZScpLFxuICAgICAgaHJlZjogJy8nLFxuICAgIH0sXG4gICAge1xuICAgICAgbGFiZWw6IHQoJ3Rhcm90JyksXG4gICAgICBocmVmOiAnL3Rhcm90JyxcbiAgICAgIGNoaWxkcmVuOiBbXG4gICAgICAgIHsgbGFiZWw6ICdUYXJvdCBSZWFkaW5nJywgaHJlZjogJy90YXJvdC90ZXN0JyB9LFxuICAgICAgICB7IGxhYmVsOiAnVGFyb3QgQ2FyZHMnLCBocmVmOiAnL3Rhcm90L2NhcmRzJyB9LFxuICAgICAgICB7IGxhYmVsOiAnVGFyb3QgR3VpZGUnLCBocmVmOiAnL3Rhcm90L2d1aWRlJyB9LFxuICAgICAgICB7IGxhYmVsOiAnVGFyb3QgSGlzdG9yeScsIGhyZWY6ICcvdGFyb3QvaGlzdG9yeScgfSxcbiAgICAgIF0sXG4gICAgfSxcbiAgICB7XG4gICAgICBsYWJlbDogdCgnYXN0cm9sb2d5JyksXG4gICAgICBocmVmOiAnL2FzdHJvbG9neScsXG4gICAgICBjaGlsZHJlbjogW1xuICAgICAgICB7IGxhYmVsOiAnQXN0cm9sb2d5IFRlc3QnLCBocmVmOiAnL2FzdHJvbG9neS90ZXN0JyB9LFxuICAgICAgICB7IGxhYmVsOiAnWm9kaWFjIFNpZ25zJywgaHJlZjogJy9hc3Ryb2xvZ3kvc2lnbnMnIH0sXG4gICAgICAgIHsgbGFiZWw6ICdDb21wYXRpYmlsaXR5JywgaHJlZjogJy9hc3Ryb2xvZ3kvY29tcGF0aWJpbGl0eScgfSxcbiAgICAgICAgeyBsYWJlbDogJ0hvcm9zY29wZScsIGhyZWY6ICcvYXN0cm9sb2d5L2hvcm9zY29wZScgfSxcbiAgICAgIF0sXG4gICAgfSxcbiAgICB7XG4gICAgICBsYWJlbDogdCgnbnVtZXJvbG9neScpLFxuICAgICAgaHJlZjogJy9udW1lcm9sb2d5JyxcbiAgICAgIGNoaWxkcmVuOiBbXG4gICAgICAgIHsgbGFiZWw6ICdOdW1lcm9sb2d5IFRlc3QnLCBocmVmOiAnL251bWVyb2xvZ3kvdGVzdCcgfSxcbiAgICAgICAgeyBsYWJlbDogJ051bWJlciBDYWxjdWxhdG9yJywgaHJlZjogJy9udW1lcm9sb2d5L2NhbGN1bGF0b3InIH0sXG4gICAgICAgIHsgbGFiZWw6ICdOdW1iZXIgTWVhbmluZ3MnLCBocmVmOiAnL251bWVyb2xvZ3kvbWVhbmluZ3MnIH0sXG4gICAgICBdLFxuICAgIH0sXG4gICAge1xuICAgICAgbGFiZWw6IHQoJ2Jsb2cnKSxcbiAgICAgIGhyZWY6ICcvYmxvZycsXG4gICAgfSxcbiAgXTtcblxuICBjb25zdCB0b2dnbGVNZW51ID0gKCkgPT4ge1xuICAgIHNldElzTWVudU9wZW4oIWlzTWVudU9wZW4pO1xuICAgIHNldEFjdGl2ZURyb3Bkb3duKG51bGwpO1xuICB9O1xuXG4gIGNvbnN0IHRvZ2dsZURyb3Bkb3duID0gKGxhYmVsOiBzdHJpbmcpID0+IHtcbiAgICBzZXRBY3RpdmVEcm9wZG93bihhY3RpdmVEcm9wZG93biA9PT0gbGFiZWwgPyBudWxsIDogbGFiZWwpO1xuICB9O1xuXG4gIHJldHVybiAoXG4gICAgPGhlYWRlciBjbGFzc05hbWU9XCJzdGlja3kgdG9wLTAgei01MCB3LWZ1bGwgYm9yZGVyLWIgYm9yZGVyLWJvcmRlci80MCBiZy1iYWNrZ3JvdW5kLzk1IGJhY2tkcm9wLWJsdXIgc3VwcG9ydHMtW2JhY2tkcm9wLWZpbHRlcl06YmctYmFja2dyb3VuZC82MFwiPlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb250YWluZXIgbXgtYXV0byBweC00XCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBoLTE2IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICB7LyogTG9nbyAqL31cbiAgICAgICAgICA8TGluayBocmVmPVwiL1wiIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoLTggdy04IHJvdW5kZWQtZnVsbCBiZy1teXN0aWNhbC1ncmFkaWVudCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIGZvbnQtYm9sZCB0ZXh0LXNtXCI+TTwvc3Bhbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZm9udC1teXN0aWNhbCB0ZXh0LXhsIGZvbnQtYm9sZCBiZy1teXN0aWNhbC1ncmFkaWVudCBiZy1jbGlwLXRleHQgdGV4dC10cmFuc3BhcmVudFwiPlxuICAgICAgICAgICAgICBNeXN0aWNhbFxuICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgIDwvTGluaz5cblxuICAgICAgICAgIHsvKiBEZXNrdG9wIE5hdmlnYXRpb24gKi99XG4gICAgICAgICAgPG5hdiBjbGFzc05hbWU9XCJoaWRkZW4gbWQ6ZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC04XCI+XG4gICAgICAgICAgICB7bmF2aWdhdGlvbi5tYXAoKGl0ZW0pID0+IChcbiAgICAgICAgICAgICAgPGRpdiBrZXk9e2l0ZW0ubGFiZWx9IGNsYXNzTmFtZT1cInJlbGF0aXZlIGdyb3VwXCI+XG4gICAgICAgICAgICAgICAge2l0ZW0uY2hpbGRyZW4gPyAoXG4gICAgICAgICAgICAgICAgICA8PlxuICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0xIHRleHQtZm9yZWdyb3VuZC84MCBob3Zlcjp0ZXh0LWZvcmVncm91bmQgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgICAgICAgICAgICAgIG9uTW91c2VFbnRlcj17KCkgPT4gc2V0QWN0aXZlRHJvcGRvd24oaXRlbS5sYWJlbCl9XG4gICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj57aXRlbS5sYWJlbH08L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgPENoZXZyb25Eb3duIGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxuICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgICAgICAgIHsvKiBEcm9wZG93biBNZW51ICovfVxuICAgICAgICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtjbihcbiAgICAgICAgICAgICAgICAgICAgICAgICdhYnNvbHV0ZSB0b3AtZnVsbCBsZWZ0LTAgbXQtMiB3LTQ4IGJnLWNhcmQgYm9yZGVyIGJvcmRlci1ib3JkZXIgcm91bmRlZC1sZyBzaGFkb3ctbGcgb3BhY2l0eS0wIGludmlzaWJsZSBncm91cC1ob3ZlcjpvcGFjaXR5LTEwMCBncm91cC1ob3Zlcjp2aXNpYmxlIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMCcsXG4gICAgICAgICAgICAgICAgICAgICAgICAnYmVmb3JlOmFic29sdXRlIGJlZm9yZTotdG9wLTIgYmVmb3JlOmxlZnQtMCBiZWZvcmU6dy1mdWxsIGJlZm9yZTpoLTInXG4gICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICBvbk1vdXNlTGVhdmU9eygpID0+IHNldEFjdGl2ZURyb3Bkb3duKG51bGwpfVxuICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJweS0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICB7aXRlbS5jaGlsZHJlbi5tYXAoKGNoaWxkKSA9PiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxMaW5rXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAga2V5PXtjaGlsZC5ocmVmfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhyZWY9e2NoaWxkLmhyZWZ9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmxvY2sgcHgtNCBweS0yIHRleHQtc20gdGV4dC1mb3JlZ3JvdW5kLzgwIGhvdmVyOnRleHQtZm9yZWdyb3VuZCBob3ZlcjpiZy1hY2NlbnQvNTAgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge2NoaWxkLmxhYmVsfVxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8Lz5cbiAgICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgICAgPExpbmtcbiAgICAgICAgICAgICAgICAgICAgaHJlZj17aXRlbS5ocmVmfVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LWZvcmVncm91bmQvODAgaG92ZXI6dGV4dC1mb3JlZ3JvdW5kIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAge2l0ZW0ubGFiZWx9XG4gICAgICAgICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApKX1cbiAgICAgICAgICA8L25hdj5cblxuICAgICAgICAgIHsvKiBDVEEgQnV0dG9uIGFuZCBUaGVtZSBUb2dnbGUgKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoaWRkZW4gbWQ6ZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC00XCI+XG4gICAgICAgICAgICA8VGhlbWVUb2dnbGUgLz5cbiAgICAgICAgICAgIDxCdXR0b24gdmFyaWFudD1cIm15c3RpY2FsXCIgc2l6ZT1cInNtXCI+XG4gICAgICAgICAgICAgIFN0YXJ0IEZyZWUgVGVzdFxuICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogTW9iaWxlIE1lbnUgQnV0dG9uICovfVxuICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cIm1kOmhpZGRlbiBwLTJcIlxuICAgICAgICAgICAgb25DbGljaz17dG9nZ2xlTWVudX1cbiAgICAgICAgICAgIGFyaWEtbGFiZWw9XCJUb2dnbGUgbWVudVwiXG4gICAgICAgICAgPlxuICAgICAgICAgICAge2lzTWVudU9wZW4gPyAoXG4gICAgICAgICAgICAgIDxYIGNsYXNzTmFtZT1cImgtNiB3LTZcIiAvPlxuICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgPE1lbnUgY2xhc3NOYW1lPVwiaC02IHctNlwiIC8+XG4gICAgICAgICAgICApfVxuICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogTW9iaWxlIE5hdmlnYXRpb24gKi99XG4gICAgICAgIHtpc01lbnVPcGVuICYmIChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1kOmhpZGRlbiBib3JkZXItdCBib3JkZXItYm9yZGVyLzQwIHB5LTRcIj5cbiAgICAgICAgICAgIDxuYXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgIHtuYXZpZ2F0aW9uLm1hcCgoaXRlbSkgPT4gKFxuICAgICAgICAgICAgICAgIDxkaXYga2V5PXtpdGVtLmxhYmVsfT5cbiAgICAgICAgICAgICAgICAgIHtpdGVtLmNoaWxkcmVuID8gKFxuICAgICAgICAgICAgICAgICAgICA8PlxuICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiB3LWZ1bGwgcHgtNCBweS0yIHRleHQtbGVmdCB0ZXh0LWZvcmVncm91bmQvODAgaG92ZXI6dGV4dC1mb3JlZ3JvdW5kIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHRvZ2dsZURyb3Bkb3duKGl0ZW0ubGFiZWwpfVxuICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPntpdGVtLmxhYmVsfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxDaGV2cm9uRG93blxuICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICdoLTQgdy00IHRyYW5zaXRpb24tdHJhbnNmb3JtJyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBhY3RpdmVEcm9wZG93biA9PT0gaXRlbS5sYWJlbCAmJiAncm90YXRlLTE4MCdcbiAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgICAgICAgICAge2FjdGl2ZURyb3Bkb3duID09PSBpdGVtLmxhYmVsICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicGwtNCBzcGFjZS15LTFcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAge2l0ZW0uY2hpbGRyZW4ubWFwKChjaGlsZCkgPT4gKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxMaW5rXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBrZXk9e2NoaWxkLmhyZWZ9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBocmVmPXtjaGlsZC5ocmVmfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmxvY2sgcHgtNCBweS0yIHRleHQtc20gdGV4dC1mb3JlZ3JvdW5kLzYwIGhvdmVyOnRleHQtZm9yZWdyb3VuZCB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRJc01lbnVPcGVuKGZhbHNlKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Y2hpbGQubGFiZWx9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgIDwvPlxuICAgICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgICAgPExpbmtcbiAgICAgICAgICAgICAgICAgICAgICBocmVmPXtpdGVtLmhyZWZ9XG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmxvY2sgcHgtNCBweS0yIHRleHQtZm9yZWdyb3VuZC84MCBob3Zlcjp0ZXh0LWZvcmVncm91bmQgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldElzTWVudU9wZW4oZmFsc2UpfVxuICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAge2l0ZW0ubGFiZWx9XG4gICAgICAgICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICkpfVxuXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicHgtNCBwdC00IHNwYWNlLXktM1wiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgPFRoZW1lVG9nZ2xlIC8+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPEJ1dHRvbiB2YXJpYW50PVwibXlzdGljYWxcIiBzaXplPVwic21cIiBjbGFzc05hbWU9XCJ3LWZ1bGxcIj5cbiAgICAgICAgICAgICAgICAgIFN0YXJ0IEZyZWUgVGVzdFxuICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvbmF2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApfVxuICAgICAgPC9kaXY+XG4gICAgPC9oZWFkZXI+XG4gICk7XG59XG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJMaW5rIiwidXNlVHJhbnNsYXRpb25zIiwiTWVudSIsIlgiLCJDaGV2cm9uRG93biIsIkJ1dHRvbiIsIlRoZW1lVG9nZ2xlIiwiY24iLCJIZWFkZXIiLCJ0IiwiaXNNZW51T3BlbiIsInNldElzTWVudU9wZW4iLCJhY3RpdmVEcm9wZG93biIsInNldEFjdGl2ZURyb3Bkb3duIiwibmF2aWdhdGlvbiIsImxhYmVsIiwiaHJlZiIsImNoaWxkcmVuIiwidG9nZ2xlTWVudSIsInRvZ2dsZURyb3Bkb3duIiwiaGVhZGVyIiwiY2xhc3NOYW1lIiwiZGl2Iiwic3BhbiIsIm5hdiIsIm1hcCIsIml0ZW0iLCJidXR0b24iLCJvbk1vdXNlRW50ZXIiLCJvbk1vdXNlTGVhdmUiLCJjaGlsZCIsInZhcmlhbnQiLCJzaXplIiwib25DbGljayIsImFyaWEtbGFiZWwiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/header.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-lg text-sm font-medium transition-all duration-300 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-mystical-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-mystical-600 text-white hover:bg-mystical-700 hover:shadow-mystical active:bg-mystical-800\",\n            destructive: \"bg-red-600 text-white hover:bg-red-700 hover:shadow-lg active:bg-red-800\",\n            outline: \"border border-mystical-300 bg-transparent text-mystical-700 hover:bg-mystical-50 hover:text-mystical-800 dark:border-mystical-600 dark:text-mystical-400 dark:hover:bg-mystical-900/20\",\n            secondary: \"bg-mystical-100 text-mystical-800 hover:bg-mystical-200 hover:shadow-sm dark:bg-mystical-800/20 dark:text-mystical-200 dark:hover:bg-mystical-800/40\",\n            ghost: \"text-mystical-700 hover:bg-mystical-100 hover:text-mystical-800 dark:text-mystical-400 dark:hover:bg-mystical-900/20 dark:hover:text-mystical-300\",\n            link: \"text-mystical-600 underline-offset-4 hover:underline hover:text-mystical-700\",\n            mystical: \"bg-gradient-to-r from-mystical-500 to-mystical-600 text-white hover:from-mystical-600 hover:to-mystical-700 hover:shadow-mystical hover:scale-105 active:scale-100\",\n            golden: \"bg-gradient-to-r from-gold-500 to-gold-600 text-white hover:from-gold-600 hover:to-gold-700 hover:shadow-gold hover:scale-105 active:scale-100\",\n            glass: \"bg-white/10 backdrop-blur-md border border-white/20 text-white hover:bg-white/20 hover:border-white/30\"\n        },\n        size: {\n            xs: \"h-8 px-3 text-xs rounded-md\",\n            sm: \"h-9 px-4 text-sm rounded-md\",\n            default: \"h-10 px-6 py-2\",\n            lg: \"h-11 px-8 text-base\",\n            xl: \"h-12 px-10 text-lg\",\n            icon: \"h-10 w-10 p-0\",\n            \"icon-sm\": \"h-8 w-8 p-0\",\n            \"icon-lg\": \"h-12 w-12 p-0\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, loading = false, leftIcon, rightIcon, loadingText, fullWidth = false, children, disabled, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size\n        }), fullWidth && \"w-full\", className),\n        ref: ref,\n        disabled: disabled || loading,\n        ...props,\n        children: [\n            loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"mr-2 h-4 w-4 animate-spin\",\n                xmlns: \"http://www.w3.org/2000/svg\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                \"aria-hidden\": \"true\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                        className: \"opacity-25\",\n                        cx: \"12\",\n                        cy: \"12\",\n                        r: \"10\",\n                        stroke: \"currentColor\",\n                        strokeWidth: \"4\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\button.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        className: \"opacity-75\",\n                        fill: \"currentColor\",\n                        d: \"m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\button.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\button.tsx\",\n                lineNumber: 83,\n                columnNumber: 11\n            }, undefined),\n            !loading && leftIcon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"flex-shrink-0\", children && \"mr-2\"),\n                children: leftIcon\n            }, void 0, false, {\n                fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\button.tsx\",\n                lineNumber: 106,\n                columnNumber: 11\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"flex-1 truncate\",\n                children: loading && loadingText ? loadingText : children\n            }, void 0, false, {\n                fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\button.tsx\",\n                lineNumber: 110,\n                columnNumber: 9\n            }, undefined),\n            !loading && rightIcon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"flex-shrink-0\", children && \"ml-2\"),\n                children: rightIcon\n            }, void 0, false, {\n                fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\button.tsx\",\n                lineNumber: 114,\n                columnNumber: 11\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 72,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/theme-toggle.tsx":
/*!********************************************!*\
  !*** ./src/components/ui/theme-toggle.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeToggle: () => (/* binding */ ThemeToggle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Moon,Sun!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/sun.js\");\n/* harmony import */ var _barrel_optimize_names_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Moon,Sun!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/moon.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ThemeToggle auto */ \n\n\nfunction ThemeToggle() {\n    const [isDark, setIsDark] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setMounted(true);\n        // 检查当前主题状态\n        const savedTheme = localStorage.getItem(\"theme\");\n        const shouldBeDark = savedTheme === \"dark\";\n        setIsDark(shouldBeDark);\n        // 应用主题\n        if (shouldBeDark) {\n            document.documentElement.classList.add(\"dark\");\n        } else {\n            document.documentElement.classList.remove(\"dark\");\n        }\n    }, []);\n    const toggleTheme = ()=>{\n        const newIsDark = !isDark;\n        setIsDark(newIsDark);\n        // 更新DOM和本地存储\n        if (newIsDark) {\n            document.documentElement.classList.add(\"dark\");\n            localStorage.setItem(\"theme\", \"dark\");\n        } else {\n            document.documentElement.classList.remove(\"dark\");\n            localStorage.setItem(\"theme\", \"light\");\n        }\n    };\n    if (!mounted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-9 w-9 inline-flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                className: \"h-5 w-5 text-foreground-muted\"\n            }, void 0, false, {\n                fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\theme-toggle.tsx\",\n                lineNumber: 44,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\theme-toggle.tsx\",\n            lineNumber: 43,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        onClick: toggleTheme,\n        className: \"h-9 w-9 inline-flex items-center justify-center rounded-md transition-colors\",\n        \"aria-label\": `Switch to ${isDark ? \"light\" : \"dark\"} mode`,\n        title: `Switch to ${isDark ? \"light\" : \"dark\"} mode`,\n        children: [\n            isDark ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                className: \"h-5 w-5 text-foreground\"\n            }, void 0, false, {\n                fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\theme-toggle.tsx\",\n                lineNumber: 57,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                className: \"h-5 w-5 text-foreground\"\n            }, void 0, false, {\n                fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\theme-toggle.tsx\",\n                lineNumber: 59,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"sr-only\",\n                children: \"Toggle theme\"\n            }, void 0, false, {\n                fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\theme-toggle.tsx\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\theme-toggle.tsx\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/theme-toggle.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   copyToClipboard: () => (/* binding */ copyToClipboard),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   deepClone: () => (/* binding */ deepClone),\n/* harmony export */   delay: () => (/* binding */ delay),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatNumber: () => (/* binding */ formatNumber),\n/* harmony export */   generateId: () => (/* binding */ generateId),\n/* harmony export */   generateSlug: () => (/* binding */ generateSlug),\n/* harmony export */   getDeviceType: () => (/* binding */ getDeviceType),\n/* harmony export */   getRelativeTime: () => (/* binding */ getRelativeTime),\n/* harmony export */   isMobile: () => (/* binding */ isMobile),\n/* harmony export */   isValidEmail: () => (/* binding */ isValidEmail),\n/* harmony export */   throttle: () => (/* binding */ throttle),\n/* harmony export */   truncateText: () => (/* binding */ truncateText)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\n/**\n * 合并Tailwind CSS类名\n */ function cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n/**\n * 格式化日期\n */ function formatDate(date, locale = \"en-US\", options = {}) {\n    const dateObj = typeof date === \"string\" ? new Date(date) : date;\n    const defaultOptions = {\n        year: \"numeric\",\n        month: \"long\",\n        day: \"numeric\"\n    };\n    return new Intl.DateTimeFormat(locale, {\n        ...defaultOptions,\n        ...options\n    }).format(dateObj);\n}\n/**\n * 生成SEO友好的URL slug\n */ function generateSlug(text) {\n    return text.toLowerCase().trim().replace(/[^\\w\\s-]/g, \"\") // 移除特殊字符\n    .replace(/[\\s_-]+/g, \"-\") // 替换空格和下划线为连字符\n    .replace(/^-+|-+$/g, \"\"); // 移除开头和结尾的连字符\n}\n/**\n * 截断文本\n */ function truncateText(text, maxLength) {\n    if (text.length <= maxLength) return text;\n    return text.slice(0, maxLength).trim() + \"...\";\n}\n/**\n * 延迟函数\n */ function delay(ms) {\n    return new Promise((resolve)=>setTimeout(resolve, ms));\n}\n/**\n * 防抖函数\n */ function debounce(func, wait) {\n    let timeout;\n    return (...args)=>{\n        clearTimeout(timeout);\n        timeout = setTimeout(()=>func(...args), wait);\n    };\n}\n/**\n * 节流函数\n */ function throttle(func, limit) {\n    let inThrottle;\n    return (...args)=>{\n        if (!inThrottle) {\n            func(...args);\n            inThrottle = true;\n            setTimeout(()=>inThrottle = false, limit);\n        }\n    };\n}\n/**\n * 生成随机ID\n */ function generateId(length = 8) {\n    const chars = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789\";\n    let result = \"\";\n    for(let i = 0; i < length; i++){\n        result += chars.charAt(Math.floor(Math.random() * chars.length));\n    }\n    return result;\n}\n/**\n * 验证邮箱格式\n */ function isValidEmail(email) {\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n}\n/**\n * 获取相对时间\n */ function getRelativeTime(date, locale = \"en\") {\n    const dateObj = typeof date === \"string\" ? new Date(date) : date;\n    const now = new Date();\n    const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000);\n    const rtf = new Intl.RelativeTimeFormat(locale, {\n        numeric: \"auto\"\n    });\n    if (diffInSeconds < 60) {\n        return rtf.format(-diffInSeconds, \"second\");\n    } else if (diffInSeconds < 3600) {\n        return rtf.format(-Math.floor(diffInSeconds / 60), \"minute\");\n    } else if (diffInSeconds < 86400) {\n        return rtf.format(-Math.floor(diffInSeconds / 3600), \"hour\");\n    } else if (diffInSeconds < 2592000) {\n        return rtf.format(-Math.floor(diffInSeconds / 86400), \"day\");\n    } else if (diffInSeconds < 31536000) {\n        return rtf.format(-Math.floor(diffInSeconds / 2592000), \"month\");\n    } else {\n        return rtf.format(-Math.floor(diffInSeconds / 31536000), \"year\");\n    }\n}\n/**\n * 深度克隆对象\n */ function deepClone(obj) {\n    if (obj === null || typeof obj !== \"object\") return obj;\n    if (obj instanceof Date) return new Date(obj.getTime());\n    if (obj instanceof Array) return obj.map((item)=>deepClone(item));\n    if (typeof obj === \"object\") {\n        const clonedObj = {};\n        for(const key in obj){\n            if (obj.hasOwnProperty(key)) {\n                clonedObj[key] = deepClone(obj[key]);\n            }\n        }\n        return clonedObj;\n    }\n    return obj;\n}\n/**\n * 格式化数字\n */ function formatNumber(number, locale = \"en-US\", options = {}) {\n    return new Intl.NumberFormat(locale, options).format(number);\n}\n/**\n * 检查是否为移动设备\n */ function isMobile() {\n    if (true) return false;\n    return window.innerWidth < 768;\n}\n/**\n * 获取设备类型\n */ function getDeviceType() {\n    if (true) return \"desktop\";\n    const width = window.innerWidth;\n    if (width < 768) return \"mobile\";\n    if (width < 1024) return \"tablet\";\n    return \"desktop\";\n}\n/**\n * 复制文本到剪贴板\n */ async function copyToClipboard(text) {\n    try {\n        if (navigator.clipboard && window.isSecureContext) {\n            await navigator.clipboard.writeText(text);\n            return true;\n        } else {\n            // 降级方案\n            const textArea = document.createElement(\"textarea\");\n            textArea.value = text;\n            textArea.style.position = \"fixed\";\n            textArea.style.left = \"-999999px\";\n            textArea.style.top = \"-999999px\";\n            document.body.appendChild(textArea);\n            textArea.focus();\n            textArea.select();\n            const result = document.execCommand(\"copy\");\n            textArea.remove();\n            return result;\n        }\n    } catch (error) {\n        console.error(\"Failed to copy text:\", error);\n        return false;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"fb7eb1c86c9a\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvc3R5bGVzL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbXlzdGljYWwtd2Vic2l0ZS8uL3NyYy9zdHlsZXMvZ2xvYmFscy5jc3M/MTA2ZiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImZiN2ViMWM4NmM5YVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/styles/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/[locale]/layout.tsx":
/*!*************************************!*\
  !*** ./src/app/[locale]/layout.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LocaleLayout),\n/* harmony export */   generateMetadata: () => (/* binding */ generateMetadata),\n/* harmony export */   generateStaticParams: () => (/* binding */ generateStaticParams)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next-intl */ \"(rsc)/./node_modules/next-intl/dist/esm/react-server/NextIntlClientProviderServer.js\");\n/* harmony import */ var next_intl_server__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-intl/server */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/RequestLocaleCache.js\");\n/* harmony import */ var next_intl_server__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next-intl/server */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getMessages.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(rsc)/./node_modules/next/dist/api/navigation.react-server.js\");\n/* harmony import */ var _components_layout_header__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/header */ \"(rsc)/./src/components/layout/header.tsx\");\n/* harmony import */ var _i18n__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/i18n */ \"(rsc)/./src/i18n.ts\");\n\n\n\n\n\n\nfunction generateStaticParams() {\n    return _i18n__WEBPACK_IMPORTED_MODULE_3__.locales.map((locale)=>({\n            locale\n        }));\n}\n// 动态生成每个语言的元数据\nasync function generateMetadata({ params: { locale } }) {\n    // 验证语言参数\n    if (!_i18n__WEBPACK_IMPORTED_MODULE_3__.locales.includes(locale)) {\n        (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.notFound)();\n    }\n    const config = _i18n__WEBPACK_IMPORTED_MODULE_3__.languageConfig[locale];\n    const baseUrl = \"http://localhost:3000\" || 0;\n    return {\n        title: {\n            template: `%s | Mystical Website`,\n            default: \"Mystical Website - Free Tarot, Astrology & Numerology Tests\"\n        },\n        description: \"Professional online mystical platform offering free tarot, astrology, and numerology tests with AI-powered analysis.\",\n        openGraph: {\n            type: \"website\",\n            locale: config.locale,\n            url: `${baseUrl}/${locale}`,\n            siteName: \"Mystical Website\"\n        },\n        alternates: {\n            canonical: `${baseUrl}/${locale}`,\n            languages: Object.fromEntries(_i18n__WEBPACK_IMPORTED_MODULE_3__.locales.map((loc)=>[\n                    _i18n__WEBPACK_IMPORTED_MODULE_3__.languageConfig[loc].hreflang,\n                    `${baseUrl}/${loc}`\n                ]))\n        },\n        other: {\n            \"content-language\": config.locale\n        }\n    };\n}\nasync function LocaleLayout({ children, params: { locale } }) {\n    // 验证语言参数\n    if (!_i18n__WEBPACK_IMPORTED_MODULE_3__.locales.includes(locale)) {\n        (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.notFound)();\n    }\n    // 启用静态渲染\n    (0,next_intl_server__WEBPACK_IMPORTED_MODULE_4__.setCachedRequestLocale)(locale);\n    // 获取消息\n    const messages = await (0,next_intl_server__WEBPACK_IMPORTED_MODULE_5__[\"default\"])();\n    // 获取语言配置\n    const config = _i18n__WEBPACK_IMPORTED_MODULE_3__.languageConfig[locale];\n    const isRTL = _i18n__WEBPACK_IMPORTED_MODULE_3__.rtlLocales.includes(locale);\n    // 根据语言选择字体类\n    const getFontClass = (fontFamily)=>{\n        switch(fontFamily){\n            case \"chinese\":\n                return \"font-noto-sans-sc\";\n            case \"japanese\":\n                return \"font-noto-sans-jp\";\n            case \"hindi\":\n                return \"font-noto-sans\";\n            default:\n                return \"font-inter\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: locale,\n        dir: config.direction,\n        className: getFontClass(config.fontFamily),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_intl__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                messages: messages,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `\n              min-h-screen bg-background\n              ${isRTL ? \"rtl\" : \"ltr\"}\n              ${getFontClass(config.fontFamily)}\n            `,\n                    style: {\n                        \"--expansion-factor\": config.expansionFactor\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_header__WEBPACK_IMPORTED_MODULE_2__.Header, {}, void 0, false, {\n                            fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\layout.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\layout.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\layout.tsx\",\n                    lineNumber: 100,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\layout.tsx\",\n                lineNumber: 99,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\layout.tsx\",\n            lineNumber: 98,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\layout.tsx\",\n        lineNumber: 93,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL1tsb2NhbGVdL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7O0FBQW1EO0FBQ2M7QUFDdEI7QUFHUztBQUNzQjtBQU9uRSxTQUFTUTtJQUNkLE9BQU9ILDBDQUFPQSxDQUFDSSxHQUFHLENBQUMsQ0FBQ0MsU0FBWTtZQUFFQTtRQUFPO0FBQzNDO0FBRUEsZUFBZTtBQUNSLGVBQWVDLGlCQUFpQixFQUNyQ0MsUUFBUSxFQUFFRixNQUFNLEVBQUUsRUFHbkI7SUFDQyxTQUFTO0lBQ1QsSUFBSSxDQUFDTCwwQ0FBT0EsQ0FBQ1EsUUFBUSxDQUFDSCxTQUFtQjtRQUN2Q1AseURBQVFBO0lBQ1Y7SUFFQSxNQUFNVyxTQUFTUixpREFBYyxDQUFDSSxPQUFpQjtJQUMvQyxNQUFNSyxVQUFVQyx1QkFBa0MsSUFBSTtJQUV0RCxPQUFPO1FBQ0xFLE9BQU87WUFDTEMsVUFBVSxDQUFDLHFCQUFxQixDQUFDO1lBQ2pDQyxTQUFTO1FBQ1g7UUFDQUMsYUFBYTtRQUNiQyxXQUFXO1lBQ1RDLE1BQU07WUFDTmIsUUFBUUksT0FBT0osTUFBTTtZQUNyQmMsS0FBSyxDQUFDLEVBQUVULFFBQVEsQ0FBQyxFQUFFTCxPQUFPLENBQUM7WUFDM0JlLFVBQVU7UUFDWjtRQUNBQyxZQUFZO1lBQ1ZDLFdBQVcsQ0FBQyxFQUFFWixRQUFRLENBQUMsRUFBRUwsT0FBTyxDQUFDO1lBQ2pDa0IsV0FBV0MsT0FBT0MsV0FBVyxDQUMzQnpCLDBDQUFPQSxDQUFDSSxHQUFHLENBQUNzQixDQUFBQSxNQUFPO29CQUNqQnpCLGlEQUFjLENBQUN5QixJQUFJLENBQUNDLFFBQVE7b0JBQzVCLENBQUMsRUFBRWpCLFFBQVEsQ0FBQyxFQUFFZ0IsSUFBSSxDQUFDO2lCQUNwQjtRQUVMO1FBQ0FFLE9BQU87WUFDTCxvQkFBb0JuQixPQUFPSixNQUFNO1FBQ25DO0lBQ0Y7QUFDRjtBQUVlLGVBQWV3QixhQUFhLEVBQ3pDQyxRQUFRLEVBQ1J2QixRQUFRLEVBQUVGLE1BQU0sRUFBRSxFQUNBO0lBQ2xCLFNBQVM7SUFDVCxJQUFJLENBQUNMLDBDQUFPQSxDQUFDUSxRQUFRLENBQUNILFNBQW1CO1FBQ3ZDUCx5REFBUUE7SUFDVjtJQUVBLFNBQVM7SUFDVEQsd0VBQWdCQSxDQUFDUTtJQUVqQixPQUFPO0lBQ1AsTUFBTTBCLFdBQVcsTUFBTW5DLDREQUFXQTtJQUVsQyxTQUFTO0lBQ1QsTUFBTWEsU0FBU1IsaURBQWMsQ0FBQ0ksT0FBaUI7SUFDL0MsTUFBTTJCLFFBQVE5Qiw2Q0FBVUEsQ0FBQ00sUUFBUSxDQUFDSDtJQUVsQyxZQUFZO0lBQ1osTUFBTTRCLGVBQWUsQ0FBQ0M7UUFDcEIsT0FBUUE7WUFDTixLQUFLO2dCQUNILE9BQU87WUFDVCxLQUFLO2dCQUNILE9BQU87WUFDVCxLQUFLO2dCQUNILE9BQU87WUFDVDtnQkFDRSxPQUFPO1FBQ1g7SUFDRjtJQUVBLHFCQUNFLDhEQUFDQztRQUNDQyxNQUFNL0I7UUFDTmdDLEtBQUs1QixPQUFPNkIsU0FBUztRQUNyQkMsV0FBV04sYUFBYXhCLE9BQU95QixVQUFVO2tCQUV6Qyw0RUFBQ007c0JBQ0MsNEVBQUM3QyxpREFBc0JBO2dCQUFDb0MsVUFBVUE7MEJBQ2hDLDRFQUFDVTtvQkFDQ0YsV0FBVyxDQUFDOztjQUVWLEVBQUVQLFFBQVEsUUFBUSxNQUFNO2NBQ3hCLEVBQUVDLGFBQWF4QixPQUFPeUIsVUFBVSxFQUFFO1lBQ3BDLENBQUM7b0JBQ0RRLE9BQU87d0JBQ0wsc0JBQXNCakMsT0FBT2tDLGVBQWU7b0JBQzlDOztzQ0FFQSw4REFBQzVDLDZEQUFNQTs7Ozs7c0NBQ1AsOERBQUM2QztzQ0FBTWQ7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU1uQiIsInNvdXJjZXMiOlsid2VicGFjazovL215c3RpY2FsLXdlYnNpdGUvLi9zcmMvYXBwL1tsb2NhbGVdL2xheW91dC50c3g/ODUzOCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBOZXh0SW50bENsaWVudFByb3ZpZGVyIH0gZnJvbSAnbmV4dC1pbnRsJztcbmltcG9ydCB7IGdldE1lc3NhZ2VzLCBzZXRSZXF1ZXN0TG9jYWxlIH0gZnJvbSAnbmV4dC1pbnRsL3NlcnZlcic7XG5pbXBvcnQgeyBub3RGb3VuZCB9IGZyb20gJ25leHQvbmF2aWdhdGlvbic7XG5pbXBvcnQgdHlwZSB7IE1ldGFkYXRhIH0gZnJvbSAnbmV4dCc7XG5cbmltcG9ydCB7IEhlYWRlciB9IGZyb20gJ0AvY29tcG9uZW50cy9sYXlvdXQvaGVhZGVyJztcbmltcG9ydCB7IGxvY2FsZXMsIGxhbmd1YWdlQ29uZmlnLCBydGxMb2NhbGVzLCB0eXBlIExvY2FsZSB9IGZyb20gJ0AvaTE4bic7XG5cbmludGVyZmFjZSBMb2NhbGVMYXlvdXRQcm9wcyB7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGU7XG4gIHBhcmFtczogeyBsb2NhbGU6IHN0cmluZyB9O1xufVxuXG5leHBvcnQgZnVuY3Rpb24gZ2VuZXJhdGVTdGF0aWNQYXJhbXMoKSB7XG4gIHJldHVybiBsb2NhbGVzLm1hcCgobG9jYWxlKSA9PiAoeyBsb2NhbGUgfSkpO1xufVxuXG4vLyDliqjmgIHnlJ/miJDmr4/kuKror63oqIDnmoTlhYPmlbDmja5cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBnZW5lcmF0ZU1ldGFkYXRhKHtcbiAgcGFyYW1zOiB7IGxvY2FsZSB9LFxufToge1xuICBwYXJhbXM6IHsgbG9jYWxlOiBzdHJpbmcgfTtcbn0pOiBQcm9taXNlPE1ldGFkYXRhPiB7XG4gIC8vIOmqjOivgeivreiogOWPguaVsFxuICBpZiAoIWxvY2FsZXMuaW5jbHVkZXMobG9jYWxlIGFzIExvY2FsZSkpIHtcbiAgICBub3RGb3VuZCgpO1xuICB9XG5cbiAgY29uc3QgY29uZmlnID0gbGFuZ3VhZ2VDb25maWdbbG9jYWxlIGFzIExvY2FsZV07XG4gIGNvbnN0IGJhc2VVcmwgPSBwcm9jZXNzLmVudlsnTkVYVF9QVUJMSUNfQVBQX1VSTCddIHx8ICdodHRwczovL215c3RpY2FsLXdlYnNpdGUuY29tJztcblxuICByZXR1cm4ge1xuICAgIHRpdGxlOiB7XG4gICAgICB0ZW1wbGF0ZTogYCVzIHwgTXlzdGljYWwgV2Vic2l0ZWAsXG4gICAgICBkZWZhdWx0OiAnTXlzdGljYWwgV2Vic2l0ZSAtIEZyZWUgVGFyb3QsIEFzdHJvbG9neSAmIE51bWVyb2xvZ3kgVGVzdHMnLFxuICAgIH0sXG4gICAgZGVzY3JpcHRpb246ICdQcm9mZXNzaW9uYWwgb25saW5lIG15c3RpY2FsIHBsYXRmb3JtIG9mZmVyaW5nIGZyZWUgdGFyb3QsIGFzdHJvbG9neSwgYW5kIG51bWVyb2xvZ3kgdGVzdHMgd2l0aCBBSS1wb3dlcmVkIGFuYWx5c2lzLicsXG4gICAgb3BlbkdyYXBoOiB7XG4gICAgICB0eXBlOiAnd2Vic2l0ZScsXG4gICAgICBsb2NhbGU6IGNvbmZpZy5sb2NhbGUsXG4gICAgICB1cmw6IGAke2Jhc2VVcmx9LyR7bG9jYWxlfWAsXG4gICAgICBzaXRlTmFtZTogJ015c3RpY2FsIFdlYnNpdGUnLFxuICAgIH0sXG4gICAgYWx0ZXJuYXRlczoge1xuICAgICAgY2Fub25pY2FsOiBgJHtiYXNlVXJsfS8ke2xvY2FsZX1gLFxuICAgICAgbGFuZ3VhZ2VzOiBPYmplY3QuZnJvbUVudHJpZXMoXG4gICAgICAgIGxvY2FsZXMubWFwKGxvYyA9PiBbXG4gICAgICAgICAgbGFuZ3VhZ2VDb25maWdbbG9jXS5ocmVmbGFuZyxcbiAgICAgICAgICBgJHtiYXNlVXJsfS8ke2xvY31gXG4gICAgICAgIF0pXG4gICAgICApLFxuICAgIH0sXG4gICAgb3RoZXI6IHtcbiAgICAgICdjb250ZW50LWxhbmd1YWdlJzogY29uZmlnLmxvY2FsZSxcbiAgICB9LFxuICB9O1xufVxuXG5leHBvcnQgZGVmYXVsdCBhc3luYyBmdW5jdGlvbiBMb2NhbGVMYXlvdXQoe1xuICBjaGlsZHJlbixcbiAgcGFyYW1zOiB7IGxvY2FsZSB9LFxufTogTG9jYWxlTGF5b3V0UHJvcHMpIHtcbiAgLy8g6aqM6K+B6K+t6KiA5Y+C5pWwXG4gIGlmICghbG9jYWxlcy5pbmNsdWRlcyhsb2NhbGUgYXMgTG9jYWxlKSkge1xuICAgIG5vdEZvdW5kKCk7XG4gIH1cblxuICAvLyDlkK/nlKjpnZnmgIHmuLLmn5NcbiAgc2V0UmVxdWVzdExvY2FsZShsb2NhbGUpO1xuXG4gIC8vIOiOt+WPlua2iOaBr1xuICBjb25zdCBtZXNzYWdlcyA9IGF3YWl0IGdldE1lc3NhZ2VzKCk7XG5cbiAgLy8g6I635Y+W6K+t6KiA6YWN572uXG4gIGNvbnN0IGNvbmZpZyA9IGxhbmd1YWdlQ29uZmlnW2xvY2FsZSBhcyBMb2NhbGVdO1xuICBjb25zdCBpc1JUTCA9IHJ0bExvY2FsZXMuaW5jbHVkZXMobG9jYWxlKTtcblxuICAvLyDmoLnmja7or63oqIDpgInmi6nlrZfkvZPnsbtcbiAgY29uc3QgZ2V0Rm9udENsYXNzID0gKGZvbnRGYW1pbHk6IHN0cmluZykgPT4ge1xuICAgIHN3aXRjaCAoZm9udEZhbWlseSkge1xuICAgICAgY2FzZSAnY2hpbmVzZSc6XG4gICAgICAgIHJldHVybiAnZm9udC1ub3RvLXNhbnMtc2MnO1xuICAgICAgY2FzZSAnamFwYW5lc2UnOlxuICAgICAgICByZXR1cm4gJ2ZvbnQtbm90by1zYW5zLWpwJztcbiAgICAgIGNhc2UgJ2hpbmRpJzpcbiAgICAgICAgcmV0dXJuICdmb250LW5vdG8tc2Fucyc7XG4gICAgICBkZWZhdWx0OlxuICAgICAgICByZXR1cm4gJ2ZvbnQtaW50ZXInO1xuICAgIH1cbiAgfTtcblxuICByZXR1cm4gKFxuICAgIDxodG1sXG4gICAgICBsYW5nPXtsb2NhbGV9XG4gICAgICBkaXI9e2NvbmZpZy5kaXJlY3Rpb259XG4gICAgICBjbGFzc05hbWU9e2dldEZvbnRDbGFzcyhjb25maWcuZm9udEZhbWlseSl9XG4gICAgPlxuICAgICAgPGJvZHk+XG4gICAgICAgIDxOZXh0SW50bENsaWVudFByb3ZpZGVyIG1lc3NhZ2VzPXttZXNzYWdlc30+XG4gICAgICAgICAgPGRpdlxuICAgICAgICAgICAgY2xhc3NOYW1lPXtgXG4gICAgICAgICAgICAgIG1pbi1oLXNjcmVlbiBiZy1iYWNrZ3JvdW5kXG4gICAgICAgICAgICAgICR7aXNSVEwgPyAncnRsJyA6ICdsdHInfVxuICAgICAgICAgICAgICAke2dldEZvbnRDbGFzcyhjb25maWcuZm9udEZhbWlseSl9XG4gICAgICAgICAgICBgfVxuICAgICAgICAgICAgc3R5bGU9e3tcbiAgICAgICAgICAgICAgJy0tZXhwYW5zaW9uLWZhY3Rvcic6IGNvbmZpZy5leHBhbnNpb25GYWN0b3IsXG4gICAgICAgICAgICB9IGFzIFJlYWN0LkNTU1Byb3BlcnRpZXN9XG4gICAgICAgICAgPlxuICAgICAgICAgICAgPEhlYWRlciAvPlxuICAgICAgICAgICAgPG1haW4+e2NoaWxkcmVufTwvbWFpbj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9OZXh0SW50bENsaWVudFByb3ZpZGVyPlxuICAgICAgPC9ib2R5PlxuICAgIDwvaHRtbD5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJOZXh0SW50bENsaWVudFByb3ZpZGVyIiwiZ2V0TWVzc2FnZXMiLCJzZXRSZXF1ZXN0TG9jYWxlIiwibm90Rm91bmQiLCJIZWFkZXIiLCJsb2NhbGVzIiwibGFuZ3VhZ2VDb25maWciLCJydGxMb2NhbGVzIiwiZ2VuZXJhdGVTdGF0aWNQYXJhbXMiLCJtYXAiLCJsb2NhbGUiLCJnZW5lcmF0ZU1ldGFkYXRhIiwicGFyYW1zIiwiaW5jbHVkZXMiLCJjb25maWciLCJiYXNlVXJsIiwicHJvY2VzcyIsImVudiIsInRpdGxlIiwidGVtcGxhdGUiLCJkZWZhdWx0IiwiZGVzY3JpcHRpb24iLCJvcGVuR3JhcGgiLCJ0eXBlIiwidXJsIiwic2l0ZU5hbWUiLCJhbHRlcm5hdGVzIiwiY2Fub25pY2FsIiwibGFuZ3VhZ2VzIiwiT2JqZWN0IiwiZnJvbUVudHJpZXMiLCJsb2MiLCJocmVmbGFuZyIsIm90aGVyIiwiTG9jYWxlTGF5b3V0IiwiY2hpbGRyZW4iLCJtZXNzYWdlcyIsImlzUlRMIiwiZ2V0Rm9udENsYXNzIiwiZm9udEZhbWlseSIsImh0bWwiLCJsYW5nIiwiZGlyIiwiZGlyZWN0aW9uIiwiY2xhc3NOYW1lIiwiYm9keSIsImRpdiIsInN0eWxlIiwiZXhwYW5zaW9uRmFjdG9yIiwibWFpbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/[locale]/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/[locale]/page.tsx":
/*!***********************************!*\
  !*** ./src/app/[locale]/page.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage),\n/* harmony export */   generateMetadata: () => (/* binding */ generateMetadata),\n/* harmony export */   generateStaticParams: () => (/* binding */ generateStaticParams)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next-intl */ \"(rsc)/./node_modules/next-intl/dist/esm/react-server/useTranslations.js\");\n/* harmony import */ var next_intl_server__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next-intl/server */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/RequestLocaleCache.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"(rsc)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(rsc)/./src/components/ui/card.tsx\");\n/* harmony import */ var _lib_seo__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/seo */ \"(rsc)/./src/lib/seo.ts\");\n/* harmony import */ var _i18n__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/i18n */ \"(rsc)/./src/i18n.ts\");\n\n\n\n\n\n\n\nasync function generateMetadata({ params: { locale } }) {\n    return (0,_lib_seo__WEBPACK_IMPORTED_MODULE_3__.generateMetadata)({\n        title: \"Discover Your Mystical Code - Free AI-Powered Tests\",\n        description: \"Professional AI analysis for accurate insights into your personality and destiny through tarot, astrology, and numerology tests.\",\n        keywords: [\n            \"mystical tests\",\n            \"free tests\",\n            \"AI analysis\",\n            \"tarot\",\n            \"astrology\",\n            \"numerology\"\n        ],\n        locale,\n        type: \"website\"\n    });\n}\nfunction generateStaticParams() {\n    return _i18n__WEBPACK_IMPORTED_MODULE_4__.locales.map((locale)=>({\n            locale\n        }));\n}\nfunction HomePage({ params }) {\n    // 启用静态渲染\n    (0,next_intl_server__WEBPACK_IMPORTED_MODULE_5__.setCachedRequestLocale)(params.locale);\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(\"homepage\");\n    const featuredTests = [\n        {\n            title: \"Tarot Reading\",\n            description: \"Unlock the wisdom of the cards and discover insights about your past, present, and future.\",\n            href: \"/tarot/test\",\n            icon: \"\\uD83D\\uDD2E\"\n        },\n        {\n            title: \"Astrology Analysis\",\n            description: \"Explore the influence of celestial bodies on your personality and life path.\",\n            href: \"/astrology/test\",\n            icon: \"⭐\"\n        },\n        {\n            title: \"Numerology Calculator\",\n            description: \"Decode the power of numbers and reveal your life path through mystical mathematics.\",\n            href: \"/numerology/test\",\n            icon: \"\\uD83D\\uDD22\"\n        },\n        {\n            title: \"Crystal Energy\",\n            description: \"Discover which crystals resonate with your energy and can enhance your spiritual journey.\",\n            href: \"/crystal/test\",\n            icon: \"\\uD83D\\uDC8E\"\n        },\n        {\n            title: \"Palm Reading\",\n            description: \"Analyze the lines and shapes of your hands to understand your character and destiny.\",\n            href: \"/palmistry/test\",\n            icon: \"✋\"\n        },\n        {\n            title: \"Dream Interpretation\",\n            description: \"Uncover the hidden meanings in your dreams and their significance to your waking life.\",\n            href: \"/dreams/test\",\n            icon: \"\\uD83C\\uDF19\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"relative overflow-hidden bg-gradient-to-br from-background via-background-secondary to-background-tertiary\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 star-field opacity-20 dark:opacity-30\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative container-responsive py-24 lg:py-32\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center max-w-4xl mx-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-4xl md:text-6xl lg:text-7xl font-bold mb-6 text-mystical-gradient animate-fade-in\",\n                                    children: t(\"title\")\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl md:text-2xl text-foreground-secondary mb-8 animate-fade-up\",\n                                    children: t(\"subtitle\")\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                    variant: \"mystical\",\n                                    size: \"xl\",\n                                    className: \"animate-scale-in\",\n                                    children: t(\"cta\")\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                lineNumber: 76,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 bg-background-secondary/30 dark:bg-background-secondary/50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container-responsive\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl md:text-4xl font-bold mb-4 text-golden-gradient\",\n                                    children: t(\"featuredTests\")\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                                    lineNumber: 102,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg text-foreground-secondary max-w-2xl mx-auto\",\n                                    children: \"Choose from our collection of mystical tests powered by advanced AI analysis\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                                    lineNumber: 105,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                            lineNumber: 101,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                            children: featuredTests.map((test, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    variant: \"mystical\",\n                                    hover: true,\n                                    className: \"animate-fade-up\",\n                                    style: {\n                                        animationDelay: `${index * 0.1}s`\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-4xl mb-4\",\n                                                    children: test.icon\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                                                    lineNumber: 120,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                    className: \"text-xl\",\n                                                    children: test.title\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                                                    lineNumber: 121,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                    children: test.description\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                                                    lineNumber: 122,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                                            lineNumber: 119,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                            className: \"text-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                                variant: \"outline\",\n                                                className: \"w-full\",\n                                                children: \"Start Test\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                                                lineNumber: 125,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                                            lineNumber: 124,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, test.title, true, {\n                                    fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                    lineNumber: 100,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                lineNumber: 99,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 bg-background\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container-responsive\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-8 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-fade-up\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-4xl md:text-5xl font-bold text-mystical-gradient mb-2\",\n                                        children: \"100K+\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-foreground-secondary\",\n                                        children: t(\"stats.users\")\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                                        lineNumber: 143,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                                lineNumber: 139,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-fade-up\",\n                                style: {\n                                    animationDelay: \"0.1s\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-4xl md:text-5xl font-bold text-golden-gradient mb-2\",\n                                        children: \"95%\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-foreground-secondary\",\n                                        children: t(\"stats.accuracy\")\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-fade-up\",\n                                style: {\n                                    animationDelay: \"0.2s\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-4xl md:text-5xl font-bold text-mystical-gradient mb-2\",\n                                        children: \"6\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-foreground-secondary\",\n                                        children: t(\"stats.languages\")\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                    lineNumber: 137,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                lineNumber: 136,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 bg-gradient-to-r from-primary-500/10 to-accent-500/10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container-responsive text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl md:text-4xl font-bold mb-4\",\n                            children: \"Ready to Discover Your Mystical Path?\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-foreground-secondary mb-8 max-w-2xl mx-auto\",\n                            children: \"Join thousands of users who have already unlocked their spiritual insights with our AI-powered mystical tests.\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                            lineNumber: 167,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                            variant: \"mystical\",\n                            size: \"xl\",\n                            children: \"Start Your Journey\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                            lineNumber: 170,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                    lineNumber: 163,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                lineNumber: 162,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n        lineNumber: 74,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/[locale]/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\",\"display\":\"swap\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-inter\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Noto_Sans_SC_arguments_subsets_latin_weight_400_500_700_variable_font_noto_sans_sc_display_swap_variableName_notoSansSC___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Noto_Sans_SC\",\"arguments\":[{\"subsets\":[\"latin\"],\"weight\":[\"400\",\"500\",\"700\"],\"variable\":\"--font-noto-sans-sc\",\"display\":\"swap\"}],\"variableName\":\"notoSansSC\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Noto_Sans_SC\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"weight\\\":[\\\"400\\\",\\\"500\\\",\\\"700\\\"],\\\"variable\\\":\\\"--font-noto-sans-sc\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"notoSansSC\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Noto_Sans_SC_arguments_subsets_latin_weight_400_500_700_variable_font_noto_sans_sc_display_swap_variableName_notoSansSC___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Noto_Sans_SC_arguments_subsets_latin_weight_400_500_700_variable_font_noto_sans_sc_display_swap_variableName_notoSansSC___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Noto_Sans_JP_arguments_subsets_latin_weight_400_500_700_variable_font_noto_sans_jp_display_swap_variableName_notoSansJP___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Noto_Sans_JP\",\"arguments\":[{\"subsets\":[\"latin\"],\"weight\":[\"400\",\"500\",\"700\"],\"variable\":\"--font-noto-sans-jp\",\"display\":\"swap\"}],\"variableName\":\"notoSansJP\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Noto_Sans_JP\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"weight\\\":[\\\"400\\\",\\\"500\\\",\\\"700\\\"],\\\"variable\\\":\\\"--font-noto-sans-jp\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"notoSansJP\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Noto_Sans_JP_arguments_subsets_latin_weight_400_500_700_variable_font_noto_sans_jp_display_swap_variableName_notoSansJP___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Noto_Sans_JP_arguments_subsets_latin_weight_400_500_700_variable_font_noto_sans_jp_display_swap_variableName_notoSansJP___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Noto_Sans_arguments_subsets_latin_devanagari_weight_400_500_700_variable_font_noto_sans_display_swap_variableName_notoSans___WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Noto_Sans\",\"arguments\":[{\"subsets\":[\"latin\",\"devanagari\"],\"weight\":[\"400\",\"500\",\"700\"],\"variable\":\"--font-noto-sans\",\"display\":\"swap\"}],\"variableName\":\"notoSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Noto_Sans\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\",\\\"devanagari\\\"],\\\"weight\\\":[\\\"400\\\",\\\"500\\\",\\\"700\\\"],\\\"variable\\\":\\\"--font-noto-sans\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"notoSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Noto_Sans_arguments_subsets_latin_devanagari_weight_400_500_700_variable_font_noto_sans_display_swap_variableName_notoSans___WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Noto_Sans_arguments_subsets_latin_devanagari_weight_400_500_700_variable_font_noto_sans_display_swap_variableName_notoSans___WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/styles/globals.css */ \"(rsc)/./src/styles/globals.css\");\n/* harmony import */ var _i18n__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/i18n */ \"(rsc)/./src/i18n.ts\");\n\n\n\n\n\n\n\n// 基础元数据\nconst metadata = {\n    title: \"Mystical Website - Free Tarot, Astrology & Numerology Tests\",\n    description: \"Professional online mystical platform offering free tarot, astrology, and numerology tests with AI-powered analysis. Accurate personality insights and life guidance.\",\n    keywords: \"mystical tests, free tests, AI analysis, tarot, astrology, numerology\",\n    authors: [\n        {\n            name: \"Mystical Website Team\"\n        }\n    ],\n    creator: \"Mystical Website\",\n    publisher: \"Mystical Website\",\n    robots: {\n        index: true,\n        follow: true,\n        googleBot: {\n            index: true,\n            follow: true,\n            \"max-video-preview\": -1,\n            \"max-image-preview\": \"large\",\n            \"max-snippet\": -1\n        }\n    },\n    openGraph: {\n        type: \"website\",\n        locale: \"en_US\",\n        url: \"http://localhost:3000\" || 0,\n        siteName: \"Mystical Website\",\n        title: \"Mystical Website - Free Tarot, Astrology & Numerology Tests\",\n        description: \"Professional online mystical platform offering free tarot, astrology, and numerology tests with AI-powered analysis.\"\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"Mystical Website - Free Tarot, Astrology & Numerology Tests\",\n        description: \"Professional online mystical platform offering free tarot, astrology, and numerology tests with AI-powered analysis.\",\n        creator: \"@mystical_website\"\n    },\n    verification: {\n        google: process.env[\"NEXT_PUBLIC_GOOGLE_SITE_VERIFICATION\"] || null,\n        other: {\n            \"msvalidate.01\": process.env[\"NEXT_PUBLIC_BING_SITE_VERIFICATION\"] || \"\"\n        }\n    },\n    // 添加多语言替代链接\n    alternates: {\n        canonical: \"http://localhost:3000\" || 0,\n        languages: Object.fromEntries(_i18n__WEBPACK_IMPORTED_MODULE_2__.locales.map((locale)=>[\n                _i18n__WEBPACK_IMPORTED_MODULE_2__.languageConfig[locale].hreflang,\n                `${\"http://localhost:3000\" || 0}/${locale}`\n            ]))\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        suppressHydrationWarning: true,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preload\",\n                        href: \"/fonts/inter-var.woff2\",\n                        as: \"font\",\n                        type: \"font/woff2\",\n                        crossOrigin: \"anonymous\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"dns-prefetch\",\n                        href: \"//fonts.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"dns-prefetch\",\n                        href: \"//fonts.gstatic.com\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"dns-prefetch\",\n                        href: \"//api.mystical-website.com\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 9\n                    }, this),\n                    _i18n__WEBPACK_IMPORTED_MODULE_2__.locales.map((locale)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"alternate\",\n                            hrefLang: _i18n__WEBPACK_IMPORTED_MODULE_2__.languageConfig[locale].hreflang,\n                            href: `${\"http://localhost:3000\" || 0}/${locale}`\n                        }, locale, false, {\n                            fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 11\n                        }, this)),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"alternate\",\n                        hrefLang: \"x-default\",\n                        href: \"http://localhost:3000\" || 0\n                    }, void 0, false, {\n                        fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 93,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `\n          ${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().variable)}\n          ${(next_font_google_target_css_path_src_app_layout_tsx_import_Noto_Sans_SC_arguments_subsets_latin_weight_400_500_700_variable_font_noto_sans_sc_display_swap_variableName_notoSansSC___WEBPACK_IMPORTED_MODULE_4___default().variable)}\n          ${(next_font_google_target_css_path_src_app_layout_tsx_import_Noto_Sans_JP_arguments_subsets_latin_weight_400_500_700_variable_font_noto_sans_jp_display_swap_variableName_notoSansJP___WEBPACK_IMPORTED_MODULE_5___default().variable)}\n          ${(next_font_google_target_css_path_src_app_layout_tsx_import_Noto_Sans_arguments_subsets_latin_devanagari_weight_400_500_700_variable_font_noto_sans_display_swap_variableName_notoSans___WEBPACK_IMPORTED_MODULE_6___default().variable)}\n          font-sans antialiased\n        `,\n                children: children\n            }, void 0, false, {\n                fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 122,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 92,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBT01BO0FBTUFDO0FBT0FDO0FBT0FDO0FBeEJ3QjtBQUNtQjtBQThCakQsUUFBUTtBQUNELE1BQU1HLFdBQXFCO0lBQ2hDQyxPQUFPO0lBQ1BDLGFBQWE7SUFDYkMsVUFBVTtJQUNWQyxTQUFTO1FBQUM7WUFBRUMsTUFBTTtRQUF3QjtLQUFFO0lBQzVDQyxTQUFTO0lBQ1RDLFdBQVc7SUFDWEMsUUFBUTtRQUNOQyxPQUFPO1FBQ1BDLFFBQVE7UUFDUkMsV0FBVztZQUNURixPQUFPO1lBQ1BDLFFBQVE7WUFDUixxQkFBcUIsQ0FBQztZQUN0QixxQkFBcUI7WUFDckIsZUFBZSxDQUFDO1FBQ2xCO0lBQ0Y7SUFDQUUsV0FBVztRQUNUQyxNQUFNO1FBQ05DLFFBQVE7UUFDUkMsS0FBS0MsdUJBQWtDLElBQUk7UUFDM0NFLFVBQVU7UUFDVmpCLE9BQU87UUFDUEMsYUFBYTtJQUNmO0lBQ0FpQixTQUFTO1FBQ1BDLE1BQU07UUFDTm5CLE9BQU87UUFDUEMsYUFBYTtRQUNiSSxTQUFTO0lBQ1g7SUFDQWUsY0FBYztRQUNaQyxRQUFRTixRQUFRQyxHQUFHLENBQUMsdUNBQXVDLElBQUk7UUFDL0RNLE9BQU87WUFDTCxpQkFBaUJQLFFBQVFDLEdBQUcsQ0FBQyxxQ0FBcUMsSUFBSTtRQUN4RTtJQUNGO0lBQ0EsWUFBWTtJQUNaTyxZQUFZO1FBQ1ZDLFdBQVdULHVCQUFrQyxJQUFJO1FBQ2pEVSxXQUFXQyxPQUFPQyxXQUFXLENBQzNCOUIsMENBQU9BLENBQUMrQixHQUFHLENBQUNmLENBQUFBLFNBQVU7Z0JBQ3BCZixpREFBYyxDQUFDZSxPQUFPLENBQUNnQixRQUFRO2dCQUMvQixDQUFDLEVBQUVkLHVCQUFrQyxJQUFJLEVBQStCLENBQUMsRUFBRUYsT0FBTyxDQUFDO2FBQ3BGO0lBRUw7QUFDRixFQUFFO0FBRWEsU0FBU2lCLFdBQVcsRUFDakNDLFFBQVEsRUFHVDtJQUNDLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO1FBQUtDLHdCQUF3Qjs7MEJBQ3RDLDhEQUFDQzs7a0NBRUMsOERBQUNDO3dCQUNDQyxLQUFJO3dCQUNKQyxNQUFLO3dCQUNMQyxJQUFHO3dCQUNIM0IsTUFBSzt3QkFDTDRCLGFBQVk7Ozs7OztrQ0FHZCw4REFBQ0o7d0JBQUtDLEtBQUk7d0JBQWVDLE1BQUs7Ozs7OztrQ0FDOUIsOERBQUNGO3dCQUFLQyxLQUFJO3dCQUFlQyxNQUFLOzs7Ozs7a0NBQzlCLDhEQUFDRjt3QkFBS0MsS0FBSTt3QkFBZUMsTUFBSzs7Ozs7O29CQUc3QnpDLDBDQUFPQSxDQUFDK0IsR0FBRyxDQUFDZixDQUFBQSx1QkFDWCw4REFBQ3VCOzRCQUVDQyxLQUFJOzRCQUNKSSxVQUFVM0MsaURBQWMsQ0FBQ2UsT0FBTyxDQUFDZ0IsUUFBUTs0QkFDekNTLE1BQU0sQ0FBQyxFQUFFdkIsdUJBQWtDLElBQUksRUFBK0IsQ0FBQyxFQUFFRixPQUFPLENBQUM7MkJBSHBGQTs7Ozs7a0NBTVQsOERBQUN1Qjt3QkFDQ0MsS0FBSTt3QkFDSkksVUFBUzt3QkFDVEgsTUFBTXZCLHVCQUFrQyxJQUFJOzs7Ozs7Ozs7Ozs7MEJBR2hELDhEQUFDMkI7Z0JBQ0NDLFdBQVcsQ0FBQztVQUNWLEVBQUVsRCwrTEFBYyxDQUFDO1VBQ2pCLEVBQUVDLHFPQUFtQixDQUFDO1VBQ3RCLEVBQUVDLHFPQUFtQixDQUFDO1VBQ3RCLEVBQUVDLHdPQUFpQixDQUFDOztRQUV0QixDQUFDOzBCQUVBbUM7Ozs7Ozs7Ozs7OztBQUlUIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbXlzdGljYWwtd2Vic2l0ZS8uL3NyYy9hcHAvbGF5b3V0LnRzeD81N2E5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgTWV0YWRhdGEgfSBmcm9tICduZXh0JztcbmltcG9ydCB7IEludGVyLCBOb3RvX1NhbnNfU0MsIE5vdG9fU2Fuc19KUCwgTm90b19TYW5zIH0gZnJvbSAnbmV4dC9mb250L2dvb2dsZSc7XG5cbmltcG9ydCAnQC9zdHlsZXMvZ2xvYmFscy5jc3MnO1xuaW1wb3J0IHsgbG9jYWxlcywgbGFuZ3VhZ2VDb25maWcgfSBmcm9tICdAL2kxOG4nO1xuXG4vLyDliqDovb3lpJror63oqIDlrZfkvZNcbmNvbnN0IGludGVyID0gSW50ZXIoe1xuICBzdWJzZXRzOiBbJ2xhdGluJ10sXG4gIHZhcmlhYmxlOiAnLS1mb250LWludGVyJyxcbiAgZGlzcGxheTogJ3N3YXAnLFxufSk7XG5cbmNvbnN0IG5vdG9TYW5zU0MgPSBOb3RvX1NhbnNfU0Moe1xuICBzdWJzZXRzOiBbJ2xhdGluJ10sXG4gIHdlaWdodDogWyc0MDAnLCAnNTAwJywgJzcwMCddLFxuICB2YXJpYWJsZTogJy0tZm9udC1ub3RvLXNhbnMtc2MnLFxuICBkaXNwbGF5OiAnc3dhcCcsXG59KTtcblxuY29uc3Qgbm90b1NhbnNKUCA9IE5vdG9fU2Fuc19KUCh7XG4gIHN1YnNldHM6IFsnbGF0aW4nXSxcbiAgd2VpZ2h0OiBbJzQwMCcsICc1MDAnLCAnNzAwJ10sXG4gIHZhcmlhYmxlOiAnLS1mb250LW5vdG8tc2Fucy1qcCcsXG4gIGRpc3BsYXk6ICdzd2FwJyxcbn0pO1xuXG5jb25zdCBub3RvU2FucyA9IE5vdG9fU2Fucyh7XG4gIHN1YnNldHM6IFsnbGF0aW4nLCAnZGV2YW5hZ2FyaSddLFxuICB3ZWlnaHQ6IFsnNDAwJywgJzUwMCcsICc3MDAnXSxcbiAgdmFyaWFibGU6ICctLWZvbnQtbm90by1zYW5zJyxcbiAgZGlzcGxheTogJ3N3YXAnLFxufSk7XG5cbi8vIOWfuuehgOWFg+aVsOaNrlxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcbiAgdGl0bGU6ICdNeXN0aWNhbCBXZWJzaXRlIC0gRnJlZSBUYXJvdCwgQXN0cm9sb2d5ICYgTnVtZXJvbG9neSBUZXN0cycsXG4gIGRlc2NyaXB0aW9uOiAnUHJvZmVzc2lvbmFsIG9ubGluZSBteXN0aWNhbCBwbGF0Zm9ybSBvZmZlcmluZyBmcmVlIHRhcm90LCBhc3Ryb2xvZ3ksIGFuZCBudW1lcm9sb2d5IHRlc3RzIHdpdGggQUktcG93ZXJlZCBhbmFseXNpcy4gQWNjdXJhdGUgcGVyc29uYWxpdHkgaW5zaWdodHMgYW5kIGxpZmUgZ3VpZGFuY2UuJyxcbiAga2V5d29yZHM6ICdteXN0aWNhbCB0ZXN0cywgZnJlZSB0ZXN0cywgQUkgYW5hbHlzaXMsIHRhcm90LCBhc3Ryb2xvZ3ksIG51bWVyb2xvZ3knLFxuICBhdXRob3JzOiBbeyBuYW1lOiAnTXlzdGljYWwgV2Vic2l0ZSBUZWFtJyB9XSxcbiAgY3JlYXRvcjogJ015c3RpY2FsIFdlYnNpdGUnLFxuICBwdWJsaXNoZXI6ICdNeXN0aWNhbCBXZWJzaXRlJyxcbiAgcm9ib3RzOiB7XG4gICAgaW5kZXg6IHRydWUsXG4gICAgZm9sbG93OiB0cnVlLFxuICAgIGdvb2dsZUJvdDoge1xuICAgICAgaW5kZXg6IHRydWUsXG4gICAgICBmb2xsb3c6IHRydWUsXG4gICAgICAnbWF4LXZpZGVvLXByZXZpZXcnOiAtMSxcbiAgICAgICdtYXgtaW1hZ2UtcHJldmlldyc6ICdsYXJnZScsXG4gICAgICAnbWF4LXNuaXBwZXQnOiAtMSxcbiAgICB9LFxuICB9LFxuICBvcGVuR3JhcGg6IHtcbiAgICB0eXBlOiAnd2Vic2l0ZScsXG4gICAgbG9jYWxlOiAnZW5fVVMnLFxuICAgIHVybDogcHJvY2Vzcy5lbnZbJ05FWFRfUFVCTElDX0FQUF9VUkwnXSB8fCAnaHR0cHM6Ly9teXN0aWNhbC13ZWJzaXRlLmNvbScsXG4gICAgc2l0ZU5hbWU6ICdNeXN0aWNhbCBXZWJzaXRlJyxcbiAgICB0aXRsZTogJ015c3RpY2FsIFdlYnNpdGUgLSBGcmVlIFRhcm90LCBBc3Ryb2xvZ3kgJiBOdW1lcm9sb2d5IFRlc3RzJyxcbiAgICBkZXNjcmlwdGlvbjogJ1Byb2Zlc3Npb25hbCBvbmxpbmUgbXlzdGljYWwgcGxhdGZvcm0gb2ZmZXJpbmcgZnJlZSB0YXJvdCwgYXN0cm9sb2d5LCBhbmQgbnVtZXJvbG9neSB0ZXN0cyB3aXRoIEFJLXBvd2VyZWQgYW5hbHlzaXMuJyxcbiAgfSxcbiAgdHdpdHRlcjoge1xuICAgIGNhcmQ6ICdzdW1tYXJ5X2xhcmdlX2ltYWdlJyxcbiAgICB0aXRsZTogJ015c3RpY2FsIFdlYnNpdGUgLSBGcmVlIFRhcm90LCBBc3Ryb2xvZ3kgJiBOdW1lcm9sb2d5IFRlc3RzJyxcbiAgICBkZXNjcmlwdGlvbjogJ1Byb2Zlc3Npb25hbCBvbmxpbmUgbXlzdGljYWwgcGxhdGZvcm0gb2ZmZXJpbmcgZnJlZSB0YXJvdCwgYXN0cm9sb2d5LCBhbmQgbnVtZXJvbG9neSB0ZXN0cyB3aXRoIEFJLXBvd2VyZWQgYW5hbHlzaXMuJyxcbiAgICBjcmVhdG9yOiAnQG15c3RpY2FsX3dlYnNpdGUnLFxuICB9LFxuICB2ZXJpZmljYXRpb246IHtcbiAgICBnb29nbGU6IHByb2Nlc3MuZW52WydORVhUX1BVQkxJQ19HT09HTEVfU0lURV9WRVJJRklDQVRJT04nXSB8fCBudWxsLFxuICAgIG90aGVyOiB7XG4gICAgICAnbXN2YWxpZGF0ZS4wMSc6IHByb2Nlc3MuZW52WydORVhUX1BVQkxJQ19CSU5HX1NJVEVfVkVSSUZJQ0FUSU9OJ10gfHwgJycsXG4gICAgfSxcbiAgfSxcbiAgLy8g5re75Yqg5aSa6K+t6KiA5pu/5Luj6ZO+5o6lXG4gIGFsdGVybmF0ZXM6IHtcbiAgICBjYW5vbmljYWw6IHByb2Nlc3MuZW52WydORVhUX1BVQkxJQ19BUFBfVVJMJ10gfHwgJ2h0dHBzOi8vbXlzdGljYWwtd2Vic2l0ZS5jb20nLFxuICAgIGxhbmd1YWdlczogT2JqZWN0LmZyb21FbnRyaWVzKFxuICAgICAgbG9jYWxlcy5tYXAobG9jYWxlID0+IFtcbiAgICAgICAgbGFuZ3VhZ2VDb25maWdbbG9jYWxlXS5ocmVmbGFuZyxcbiAgICAgICAgYCR7cHJvY2Vzcy5lbnZbJ05FWFRfUFVCTElDX0FQUF9VUkwnXSB8fCAnaHR0cHM6Ly9teXN0aWNhbC13ZWJzaXRlLmNvbSd9LyR7bG9jYWxlfWBcbiAgICAgIF0pXG4gICAgKSxcbiAgfSxcbn07XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcbn0pIHtcbiAgcmV0dXJuIChcbiAgICA8aHRtbCBsYW5nPVwiZW5cIiBzdXBwcmVzc0h5ZHJhdGlvbldhcm5pbmc+XG4gICAgICA8aGVhZD5cbiAgICAgICAgey8qIOmihOWKoOi9veWFs+mUruWtl+S9kyAqL31cbiAgICAgICAgPGxpbmtcbiAgICAgICAgICByZWw9XCJwcmVsb2FkXCJcbiAgICAgICAgICBocmVmPVwiL2ZvbnRzL2ludGVyLXZhci53b2ZmMlwiXG4gICAgICAgICAgYXM9XCJmb250XCJcbiAgICAgICAgICB0eXBlPVwiZm9udC93b2ZmMlwiXG4gICAgICAgICAgY3Jvc3NPcmlnaW49XCJhbm9ueW1vdXNcIlxuICAgICAgICAvPlxuICAgICAgICB7LyogRE5T6aKE5Y+WICovfVxuICAgICAgICA8bGluayByZWw9XCJkbnMtcHJlZmV0Y2hcIiBocmVmPVwiLy9mb250cy5nb29nbGVhcGlzLmNvbVwiIC8+XG4gICAgICAgIDxsaW5rIHJlbD1cImRucy1wcmVmZXRjaFwiIGhyZWY9XCIvL2ZvbnRzLmdzdGF0aWMuY29tXCIgLz5cbiAgICAgICAgPGxpbmsgcmVsPVwiZG5zLXByZWZldGNoXCIgaHJlZj1cIi8vYXBpLm15c3RpY2FsLXdlYnNpdGUuY29tXCIgLz5cblxuICAgICAgICB7Lyog5aSa6K+t6KiAaHJlZmxhbmfmoIfnrb4gKi99XG4gICAgICAgIHtsb2NhbGVzLm1hcChsb2NhbGUgPT4gKFxuICAgICAgICAgIDxsaW5rXG4gICAgICAgICAgICBrZXk9e2xvY2FsZX1cbiAgICAgICAgICAgIHJlbD1cImFsdGVybmF0ZVwiXG4gICAgICAgICAgICBocmVmTGFuZz17bGFuZ3VhZ2VDb25maWdbbG9jYWxlXS5ocmVmbGFuZ31cbiAgICAgICAgICAgIGhyZWY9e2Ake3Byb2Nlc3MuZW52WydORVhUX1BVQkxJQ19BUFBfVVJMJ10gfHwgJ2h0dHBzOi8vbXlzdGljYWwtd2Vic2l0ZS5jb20nfS8ke2xvY2FsZX1gfVxuICAgICAgICAgIC8+XG4gICAgICAgICkpfVxuICAgICAgICA8bGlua1xuICAgICAgICAgIHJlbD1cImFsdGVybmF0ZVwiXG4gICAgICAgICAgaHJlZkxhbmc9XCJ4LWRlZmF1bHRcIlxuICAgICAgICAgIGhyZWY9e3Byb2Nlc3MuZW52WydORVhUX1BVQkxJQ19BUFBfVVJMJ10gfHwgJ2h0dHBzOi8vbXlzdGljYWwtd2Vic2l0ZS5jb20nfVxuICAgICAgICAvPlxuICAgICAgPC9oZWFkPlxuICAgICAgPGJvZHlcbiAgICAgICAgY2xhc3NOYW1lPXtgXG4gICAgICAgICAgJHtpbnRlci52YXJpYWJsZX1cbiAgICAgICAgICAke25vdG9TYW5zU0MudmFyaWFibGV9XG4gICAgICAgICAgJHtub3RvU2Fuc0pQLnZhcmlhYmxlfVxuICAgICAgICAgICR7bm90b1NhbnMudmFyaWFibGV9XG4gICAgICAgICAgZm9udC1zYW5zIGFudGlhbGlhc2VkXG4gICAgICAgIGB9XG4gICAgICA+XG4gICAgICAgIHtjaGlsZHJlbn1cbiAgICAgIDwvYm9keT5cbiAgICA8L2h0bWw+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiaW50ZXIiLCJub3RvU2Fuc1NDIiwibm90b1NhbnNKUCIsIm5vdG9TYW5zIiwibG9jYWxlcyIsImxhbmd1YWdlQ29uZmlnIiwibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwia2V5d29yZHMiLCJhdXRob3JzIiwibmFtZSIsImNyZWF0b3IiLCJwdWJsaXNoZXIiLCJyb2JvdHMiLCJpbmRleCIsImZvbGxvdyIsImdvb2dsZUJvdCIsIm9wZW5HcmFwaCIsInR5cGUiLCJsb2NhbGUiLCJ1cmwiLCJwcm9jZXNzIiwiZW52Iiwic2l0ZU5hbWUiLCJ0d2l0dGVyIiwiY2FyZCIsInZlcmlmaWNhdGlvbiIsImdvb2dsZSIsIm90aGVyIiwiYWx0ZXJuYXRlcyIsImNhbm9uaWNhbCIsImxhbmd1YWdlcyIsIk9iamVjdCIsImZyb21FbnRyaWVzIiwibWFwIiwiaHJlZmxhbmciLCJSb290TGF5b3V0IiwiY2hpbGRyZW4iLCJodG1sIiwibGFuZyIsInN1cHByZXNzSHlkcmF0aW9uV2FybmluZyIsImhlYWQiLCJsaW5rIiwicmVsIiwiaHJlZiIsImFzIiwiY3Jvc3NPcmlnaW4iLCJocmVmTGFuZyIsImJvZHkiLCJjbGFzc05hbWUiLCJ2YXJpYWJsZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/layout/header.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/header.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Header: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`E:\tarot-seo\src\components\layout\header.tsx#Header`);


/***/ }),

/***/ "(rsc)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(rsc)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(rsc)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(rsc)/./src/lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-lg text-sm font-medium transition-all duration-300 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-mystical-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-mystical-600 text-white hover:bg-mystical-700 hover:shadow-mystical active:bg-mystical-800\",\n            destructive: \"bg-red-600 text-white hover:bg-red-700 hover:shadow-lg active:bg-red-800\",\n            outline: \"border border-mystical-300 bg-transparent text-mystical-700 hover:bg-mystical-50 hover:text-mystical-800 dark:border-mystical-600 dark:text-mystical-400 dark:hover:bg-mystical-900/20\",\n            secondary: \"bg-mystical-100 text-mystical-800 hover:bg-mystical-200 hover:shadow-sm dark:bg-mystical-800/20 dark:text-mystical-200 dark:hover:bg-mystical-800/40\",\n            ghost: \"text-mystical-700 hover:bg-mystical-100 hover:text-mystical-800 dark:text-mystical-400 dark:hover:bg-mystical-900/20 dark:hover:text-mystical-300\",\n            link: \"text-mystical-600 underline-offset-4 hover:underline hover:text-mystical-700\",\n            mystical: \"bg-gradient-to-r from-mystical-500 to-mystical-600 text-white hover:from-mystical-600 hover:to-mystical-700 hover:shadow-mystical hover:scale-105 active:scale-100\",\n            golden: \"bg-gradient-to-r from-gold-500 to-gold-600 text-white hover:from-gold-600 hover:to-gold-700 hover:shadow-gold hover:scale-105 active:scale-100\",\n            glass: \"bg-white/10 backdrop-blur-md border border-white/20 text-white hover:bg-white/20 hover:border-white/30\"\n        },\n        size: {\n            xs: \"h-8 px-3 text-xs rounded-md\",\n            sm: \"h-9 px-4 text-sm rounded-md\",\n            default: \"h-10 px-6 py-2\",\n            lg: \"h-11 px-8 text-base\",\n            xl: \"h-12 px-10 text-lg\",\n            icon: \"h-10 w-10 p-0\",\n            \"icon-sm\": \"h-8 w-8 p-0\",\n            \"icon-lg\": \"h-12 w-12 p-0\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, loading = false, leftIcon, rightIcon, loadingText, fullWidth = false, children, disabled, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size\n        }), fullWidth && \"w-full\", className),\n        ref: ref,\n        disabled: disabled || loading,\n        ...props,\n        children: [\n            loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"mr-2 h-4 w-4 animate-spin\",\n                xmlns: \"http://www.w3.org/2000/svg\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                \"aria-hidden\": \"true\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                        className: \"opacity-25\",\n                        cx: \"12\",\n                        cy: \"12\",\n                        r: \"10\",\n                        stroke: \"currentColor\",\n                        strokeWidth: \"4\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\button.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        className: \"opacity-75\",\n                        fill: \"currentColor\",\n                        d: \"m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\button.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\button.tsx\",\n                lineNumber: 83,\n                columnNumber: 11\n            }, undefined),\n            !loading && leftIcon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"flex-shrink-0\", children && \"mr-2\"),\n                children: leftIcon\n            }, void 0, false, {\n                fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\button.tsx\",\n                lineNumber: 106,\n                columnNumber: 11\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"flex-1 truncate\",\n                children: loading && loadingText ? loadingText : children\n            }, void 0, false, {\n                fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\button.tsx\",\n                lineNumber: 110,\n                columnNumber: 9\n            }, undefined),\n            !loading && rightIcon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"flex-shrink-0\", children && \"ml-2\"),\n                children: rightIcon\n            }, void 0, false, {\n                fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\button.tsx\",\n                lineNumber: 114,\n                columnNumber: 11\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 72,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/ui/card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/card.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle),\n/* harmony export */   cardVariants: () => (/* binding */ cardVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(rsc)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(rsc)/./src/lib/utils.ts\");\n\n\n\n\nconst cardVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"rounded-xl border bg-card text-card-foreground shadow-sm transition-all duration-300\", {\n    variants: {\n        variant: {\n            default: \"border-border bg-card hover:shadow-md\",\n            mystical: \"border-mystical-300/20 bg-gradient-to-br from-card to-mystical-50/50 shadow-mystical hover:shadow-mystical-lg hover:border-mystical-400/30 dark:to-mystical-900/20\",\n            golden: \"border-gold-300/20 bg-gradient-to-br from-card to-gold-50/50 shadow-gold hover:shadow-gold hover:border-gold-400/30 dark:to-gold-900/20\",\n            glass: \"border-white/10 bg-white/5 backdrop-blur-md hover:bg-white/10 hover:border-white/20\",\n            elevated: \"border-border bg-card shadow-lg hover:shadow-xl\",\n            outline: \"border-2 border-mystical-200 bg-transparent hover:bg-mystical-50/50 dark:border-mystical-700 dark:hover:bg-mystical-900/20\"\n        },\n        size: {\n            xs: \"p-3\",\n            sm: \"p-4\",\n            default: \"p-6\",\n            lg: \"p-8\",\n            xl: \"p-10\"\n        },\n        interactive: {\n            none: \"\",\n            hover: \"hover:-translate-y-1 hover:scale-[1.02] cursor-pointer\",\n            press: \"hover:-translate-y-1 hover:scale-[1.02] active:scale-[0.98] cursor-pointer\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\",\n        interactive: \"none\"\n    }\n});\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, interactive, hover = false, glow = false, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(cardVariants({\n            variant,\n            size,\n            interactive\n        }), hover && \"hover:-translate-y-1 hover:scale-[1.02] cursor-pointer\", glow && variant === \"mystical\" && \"animate-mystical-glow\", glow && variant === \"golden\" && \"golden-glow\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 48,\n        columnNumber: 5\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, centered = false, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"flex flex-col space-y-1.5 p-6\", centered && \"items-center text-center\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 69,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 85,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 100,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 112,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 120,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/ui/card.tsx\n");

/***/ }),

/***/ "(rsc)/./src/i18n.ts":
/*!*********************!*\
  !*** ./src/i18n.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   defaultLocale: () => (/* binding */ defaultLocale),\n/* harmony export */   fallbackChain: () => (/* binding */ fallbackChain),\n/* harmony export */   formatConfig: () => (/* binding */ formatConfig),\n/* harmony export */   languageConfig: () => (/* binding */ languageConfig),\n/* harmony export */   locales: () => (/* binding */ locales),\n/* harmony export */   pathnames: () => (/* binding */ pathnames),\n/* harmony export */   phase2Locales: () => (/* binding */ phase2Locales),\n/* harmony export */   rtlLocales: () => (/* binding */ rtlLocales)\n/* harmony export */ });\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/navigation */ \"(rsc)/./node_modules/next/dist/api/navigation.react-server.js\");\n/* harmony import */ var next_intl_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-intl/server */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getRequestConfig.js\");\n\n\n// 第一阶段：核心市场语言 (立即实施)\nconst locales = [\n    \"en\",\n    \"zh\",\n    \"es\",\n    \"pt\",\n    \"hi\",\n    \"ja\"\n];\n// 第二阶段：重要区域语言 (6个月后扩展)\nconst phase2Locales = [\n    \"de\",\n    \"fr\",\n    \"it\",\n    \"ru\",\n    \"ko\",\n    \"ar\"\n];\n// 语言配置 - 包含完整的多语言元数据\nconst languageConfig = {\n    en: {\n        name: \"English\",\n        nativeName: \"English\",\n        direction: \"ltr\",\n        region: \"Global\",\n        population: \"Global market\",\n        flag: \"\\uD83C\\uDDFA\\uD83C\\uDDF8\",\n        hreflang: \"en\",\n        locale: \"en-US\",\n        currency: \"USD\",\n        fontFamily: \"latin\",\n        expansionFactor: 1.0,\n        priority: 1\n    },\n    zh: {\n        name: \"Chinese\",\n        nativeName: \"中文\",\n        direction: \"ltr\",\n        region: \"China & Chinese communities\",\n        population: \"1.4 billion\",\n        flag: \"\\uD83C\\uDDE8\\uD83C\\uDDF3\",\n        hreflang: \"zh-CN\",\n        locale: \"zh-CN\",\n        currency: \"CNY\",\n        fontFamily: \"chinese\",\n        expansionFactor: 0.8,\n        priority: 2\n    },\n    es: {\n        name: \"Spanish\",\n        nativeName: \"Espa\\xf1ol\",\n        direction: \"ltr\",\n        region: \"Spain & Latin America\",\n        population: \"500 million\",\n        flag: \"\\uD83C\\uDDEA\\uD83C\\uDDF8\",\n        hreflang: \"es\",\n        locale: \"es-ES\",\n        currency: \"EUR\",\n        fontFamily: \"latin\",\n        expansionFactor: 1.25,\n        priority: 3\n    },\n    pt: {\n        name: \"Portuguese\",\n        nativeName: \"Portugu\\xeas\",\n        direction: \"ltr\",\n        region: \"Brazil & Portuguese-speaking countries\",\n        population: \"260 million\",\n        flag: \"\\uD83C\\uDDE7\\uD83C\\uDDF7\",\n        hreflang: \"pt-BR\",\n        locale: \"pt-BR\",\n        currency: \"BRL\",\n        fontFamily: \"latin\",\n        expansionFactor: 1.20,\n        priority: 4\n    },\n    hi: {\n        name: \"Hindi\",\n        nativeName: \"हिन्दी\",\n        direction: \"ltr\",\n        region: \"Northern India\",\n        population: \"600 million\",\n        flag: \"\\uD83C\\uDDEE\\uD83C\\uDDF3\",\n        hreflang: \"hi-IN\",\n        locale: \"hi-IN\",\n        currency: \"INR\",\n        fontFamily: \"hindi\",\n        expansionFactor: 1.40,\n        priority: 5\n    },\n    ja: {\n        name: \"Japanese\",\n        nativeName: \"日本語\",\n        direction: \"ltr\",\n        region: \"Japan\",\n        population: \"125 million\",\n        flag: \"\\uD83C\\uDDEF\\uD83C\\uDDF5\",\n        hreflang: \"ja-JP\",\n        locale: \"ja-JP\",\n        currency: \"JPY\",\n        fontFamily: \"japanese\",\n        expansionFactor: 1.10,\n        priority: 6\n    }\n};\n// 默认语言\nconst defaultLocale = \"en\";\n// RTL语言配置\nconst rtlLocales = [\n    \"ar\",\n    \"he\",\n    \"fa\",\n    \"ur\"\n];\n// 语言回退链配置\nconst fallbackChain = {\n    en: [],\n    zh: [\n        \"en\"\n    ],\n    es: [\n        \"en\"\n    ],\n    pt: [\n        \"es\",\n        \"en\"\n    ],\n    hi: [\n        \"en\"\n    ],\n    ja: [\n        \"en\"\n    ]\n};\n// 语言检测配置 - 增强版本，支持回退机制\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_intl_server__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(async ({ locale })=>{\n    // 验证传入的语言是否支持\n    if (!locales.includes(locale)) {\n        (0,next_navigation__WEBPACK_IMPORTED_MODULE_0__.notFound)();\n    }\n    try {\n        // 尝试加载主要语言文件\n        const messages = (await __webpack_require__(\"(rsc)/./messages lazy recursive ^\\\\.\\\\/.*\\\\.json$\")(`./${locale}.json`)).default;\n        // 如果有回退语言，合并消息\n        const fallbacks = fallbackChain[locale] || [];\n        let mergedMessages = messages;\n        for (const fallbackLocale of fallbacks){\n            try {\n                const fallbackMessages = (await __webpack_require__(\"(rsc)/./messages lazy recursive ^\\\\.\\\\/.*\\\\.json$\")(`./${fallbackLocale}.json`)).default;\n                mergedMessages = {\n                    ...fallbackMessages,\n                    ...mergedMessages\n                };\n            } catch (fallbackError) {\n                console.warn(`Failed to load fallback messages for locale: ${fallbackLocale}`, fallbackError);\n            }\n        }\n        return {\n            messages: mergedMessages,\n            timeZone: getTimeZoneForLocale(locale),\n            now: new Date(),\n            formats: getFormatsForLocale(locale)\n        };\n    } catch (error) {\n        console.error(`Failed to load messages for locale: ${locale}`, error);\n        // 尝试回退到默认语言\n        if (locale !== defaultLocale) {\n            try {\n                const defaultMessages = (await __webpack_require__(\"(rsc)/./messages lazy recursive ^\\\\.\\\\/.*\\\\.json$\")(`./${defaultLocale}.json`)).default;\n                return {\n                    messages: defaultMessages,\n                    timeZone: getTimeZoneForLocale(defaultLocale),\n                    now: new Date(),\n                    formats: getFormatsForLocale(defaultLocale)\n                };\n            } catch (defaultError) {\n                console.error(`Failed to load default locale messages`, defaultError);\n            }\n        }\n        (0,next_navigation__WEBPACK_IMPORTED_MODULE_0__.notFound)();\n    }\n}));\n// 根据语言获取时区\nfunction getTimeZoneForLocale(locale) {\n    const timezoneMap = {\n        en: \"America/New_York\",\n        zh: \"Asia/Shanghai\",\n        es: \"Europe/Madrid\",\n        pt: \"America/Sao_Paulo\",\n        hi: \"Asia/Kolkata\",\n        ja: \"Asia/Tokyo\"\n    };\n    return timezoneMap[locale] || \"UTC\";\n}\n// 根据语言获取格式化配置\nfunction getFormatsForLocale(locale) {\n    const formatMap = {\n        en: {\n            dateTime: {\n                short: {\n                    day: \"numeric\",\n                    month: \"short\",\n                    year: \"numeric\"\n                },\n                long: {\n                    day: \"numeric\",\n                    month: \"long\",\n                    year: \"numeric\",\n                    weekday: \"long\"\n                }\n            },\n            number: {\n                currency: {\n                    style: \"currency\",\n                    currency: \"USD\"\n                }\n            }\n        },\n        zh: {\n            dateTime: {\n                short: {\n                    day: \"numeric\",\n                    month: \"short\",\n                    year: \"numeric\"\n                },\n                long: {\n                    day: \"numeric\",\n                    month: \"long\",\n                    year: \"numeric\",\n                    weekday: \"long\"\n                }\n            },\n            number: {\n                currency: {\n                    style: \"currency\",\n                    currency: \"CNY\"\n                }\n            }\n        },\n        es: {\n            dateTime: {\n                short: {\n                    day: \"numeric\",\n                    month: \"short\",\n                    year: \"numeric\"\n                },\n                long: {\n                    day: \"numeric\",\n                    month: \"long\",\n                    year: \"numeric\",\n                    weekday: \"long\"\n                }\n            },\n            number: {\n                currency: {\n                    style: \"currency\",\n                    currency: \"EUR\"\n                }\n            }\n        },\n        pt: {\n            dateTime: {\n                short: {\n                    day: \"numeric\",\n                    month: \"short\",\n                    year: \"numeric\"\n                },\n                long: {\n                    day: \"numeric\",\n                    month: \"long\",\n                    year: \"numeric\",\n                    weekday: \"long\"\n                }\n            },\n            number: {\n                currency: {\n                    style: \"currency\",\n                    currency: \"BRL\"\n                }\n            }\n        },\n        hi: {\n            dateTime: {\n                short: {\n                    day: \"numeric\",\n                    month: \"short\",\n                    year: \"numeric\"\n                },\n                long: {\n                    day: \"numeric\",\n                    month: \"long\",\n                    year: \"numeric\",\n                    weekday: \"long\"\n                }\n            },\n            number: {\n                currency: {\n                    style: \"currency\",\n                    currency: \"INR\"\n                }\n            }\n        },\n        ja: {\n            dateTime: {\n                short: {\n                    day: \"numeric\",\n                    month: \"short\",\n                    year: \"numeric\"\n                },\n                long: {\n                    day: \"numeric\",\n                    month: \"long\",\n                    year: \"numeric\",\n                    weekday: \"long\"\n                }\n            },\n            number: {\n                currency: {\n                    style: \"currency\",\n                    currency: \"JPY\"\n                }\n            }\n        }\n    };\n    return formatMap[locale] || formatMap[defaultLocale];\n}\n// 语言路径配置\nconst pathnames = {\n    \"/\": \"/\",\n    \"/blog\": {\n        en: \"/blog\",\n        zh: \"/blog\",\n        es: \"/blog\",\n        pt: \"/blog\",\n        hi: \"/blog\",\n        ja: \"/blog\"\n    },\n    \"/tarot\": {\n        en: \"/tarot\",\n        zh: \"/tarot\",\n        es: \"/tarot\",\n        pt: \"/tarot\",\n        hi: \"/tarot\",\n        ja: \"/tarot\"\n    },\n    \"/astrology\": {\n        en: \"/astrology\",\n        zh: \"/astrology\",\n        es: \"/astrology\",\n        pt: \"/astrology\",\n        hi: \"/astrology\",\n        ja: \"/astrology\"\n    },\n    \"/numerology\": {\n        en: \"/numerology\",\n        zh: \"/numerology\",\n        es: \"/numerology\",\n        pt: \"/numerology\",\n        hi: \"/numerology\",\n        ja: \"/numerology\"\n    }\n};\n// 语言特定的格式化配置\nconst formatConfig = {\n    en: {\n        dateFormat: \"MM/dd/yyyy\",\n        timeFormat: \"12h\",\n        currency: \"USD\",\n        numberFormat: \"en-US\"\n    },\n    zh: {\n        dateFormat: \"yyyy年MM月dd日\",\n        timeFormat: \"24h\",\n        currency: \"CNY\",\n        numberFormat: \"zh-CN\"\n    },\n    es: {\n        dateFormat: \"dd/MM/yyyy\",\n        timeFormat: \"24h\",\n        currency: \"EUR\",\n        numberFormat: \"es-ES\"\n    },\n    pt: {\n        dateFormat: \"dd/MM/yyyy\",\n        timeFormat: \"24h\",\n        currency: \"BRL\",\n        numberFormat: \"pt-BR\"\n    },\n    hi: {\n        dateFormat: \"dd/MM/yyyy\",\n        timeFormat: \"12h\",\n        currency: \"INR\",\n        numberFormat: \"hi-IN\"\n    },\n    ja: {\n        dateFormat: \"yyyy年MM月dd日\",\n        timeFormat: \"24h\",\n        currency: \"JPY\",\n        numberFormat: \"ja-JP\"\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvaTE4bi50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7OztBQUEyQztBQUNTO0FBRXBELHFCQUFxQjtBQUNkLE1BQU1FLFVBQVU7SUFBQztJQUFNO0lBQU07SUFBTTtJQUFNO0lBQU07Q0FBSyxDQUFVO0FBR3JFLHVCQUF1QjtBQUNoQixNQUFNQyxnQkFBZ0I7SUFBQztJQUFNO0lBQU07SUFBTTtJQUFNO0lBQU07Q0FBSyxDQUFVO0FBTTNFLHFCQUFxQjtBQUNkLE1BQU1DLGlCQUFpQjtJQUM1QkMsSUFBSTtRQUNGQyxNQUFNO1FBQ05DLFlBQVk7UUFDWkMsV0FBVztRQUNYQyxRQUFRO1FBQ1JDLFlBQVk7UUFDWkMsTUFBTTtRQUNOQyxVQUFVO1FBQ1ZDLFFBQVE7UUFDUkMsVUFBVTtRQUNWQyxZQUFZO1FBQ1pDLGlCQUFpQjtRQUNqQkMsVUFBVTtJQUNaO0lBQ0FDLElBQUk7UUFDRlosTUFBTTtRQUNOQyxZQUFZO1FBQ1pDLFdBQVc7UUFDWEMsUUFBUTtRQUNSQyxZQUFZO1FBQ1pDLE1BQU07UUFDTkMsVUFBVTtRQUNWQyxRQUFRO1FBQ1JDLFVBQVU7UUFDVkMsWUFBWTtRQUNaQyxpQkFBaUI7UUFDakJDLFVBQVU7SUFDWjtJQUNBRSxJQUFJO1FBQ0ZiLE1BQU07UUFDTkMsWUFBWTtRQUNaQyxXQUFXO1FBQ1hDLFFBQVE7UUFDUkMsWUFBWTtRQUNaQyxNQUFNO1FBQ05DLFVBQVU7UUFDVkMsUUFBUTtRQUNSQyxVQUFVO1FBQ1ZDLFlBQVk7UUFDWkMsaUJBQWlCO1FBQ2pCQyxVQUFVO0lBQ1o7SUFDQUcsSUFBSTtRQUNGZCxNQUFNO1FBQ05DLFlBQVk7UUFDWkMsV0FBVztRQUNYQyxRQUFRO1FBQ1JDLFlBQVk7UUFDWkMsTUFBTTtRQUNOQyxVQUFVO1FBQ1ZDLFFBQVE7UUFDUkMsVUFBVTtRQUNWQyxZQUFZO1FBQ1pDLGlCQUFpQjtRQUNqQkMsVUFBVTtJQUNaO0lBQ0FJLElBQUk7UUFDRmYsTUFBTTtRQUNOQyxZQUFZO1FBQ1pDLFdBQVc7UUFDWEMsUUFBUTtRQUNSQyxZQUFZO1FBQ1pDLE1BQU07UUFDTkMsVUFBVTtRQUNWQyxRQUFRO1FBQ1JDLFVBQVU7UUFDVkMsWUFBWTtRQUNaQyxpQkFBaUI7UUFDakJDLFVBQVU7SUFDWjtJQUNBSyxJQUFJO1FBQ0ZoQixNQUFNO1FBQ05DLFlBQVk7UUFDWkMsV0FBVztRQUNYQyxRQUFRO1FBQ1JDLFlBQVk7UUFDWkMsTUFBTTtRQUNOQyxVQUFVO1FBQ1ZDLFFBQVE7UUFDUkMsVUFBVTtRQUNWQyxZQUFZO1FBQ1pDLGlCQUFpQjtRQUNqQkMsVUFBVTtJQUNaO0FBQ0YsRUFBVztBQUVYLE9BQU87QUFDQSxNQUFNTSxnQkFBd0IsS0FBSztBQUUxQyxVQUFVO0FBQ0gsTUFBTUMsYUFBZ0M7SUFBQztJQUFNO0lBQU07SUFBTTtDQUFLLENBQVU7QUFFL0UsVUFBVTtBQUNILE1BQU1DLGdCQUEwQztJQUNyRHBCLElBQUksRUFBRTtJQUNOYSxJQUFJO1FBQUM7S0FBSztJQUNWQyxJQUFJO1FBQUM7S0FBSztJQUNWQyxJQUFJO1FBQUM7UUFBTTtLQUFLO0lBQ2hCQyxJQUFJO1FBQUM7S0FBSztJQUNWQyxJQUFJO1FBQUM7S0FBSztBQUNaLEVBQUU7QUFFRix1QkFBdUI7QUFDdkIsaUVBQWVyQiw0REFBZ0JBLENBQUMsT0FBTyxFQUFFWSxNQUFNLEVBQUU7SUFDL0MsY0FBYztJQUNkLElBQUksQ0FBQ1gsUUFBUXdCLFFBQVEsQ0FBQ2IsU0FBbUI7UUFDdkNiLHlEQUFRQTtJQUNWO0lBRUEsSUFBSTtRQUNGLGFBQWE7UUFDYixNQUFNMkIsV0FBVyxDQUFDLE1BQU0seUVBQU8sR0FBYSxFQUFFZCxPQUFPLE1BQU0sR0FBR2UsT0FBTztRQUVyRSxlQUFlO1FBQ2YsTUFBTUMsWUFBWUosYUFBYSxDQUFDWixPQUFpQixJQUFJLEVBQUU7UUFDdkQsSUFBSWlCLGlCQUFpQkg7UUFFckIsS0FBSyxNQUFNSSxrQkFBa0JGLFVBQVc7WUFDdEMsSUFBSTtnQkFDRixNQUFNRyxtQkFBbUIsQ0FBQyxNQUFNLHlFQUFPLEdBQWEsRUFBRUQsZUFBZSxNQUFNLEdBQUdILE9BQU87Z0JBQ3JGRSxpQkFBaUI7b0JBQUUsR0FBR0UsZ0JBQWdCO29CQUFFLEdBQUdGLGNBQWM7Z0JBQUM7WUFDNUQsRUFBRSxPQUFPRyxlQUFlO2dCQUN0QkMsUUFBUUMsSUFBSSxDQUFDLENBQUMsNkNBQTZDLEVBQUVKLGVBQWUsQ0FBQyxFQUFFRTtZQUNqRjtRQUNGO1FBRUEsT0FBTztZQUNMTixVQUFVRztZQUNWTSxVQUFVQyxxQkFBcUJ4QjtZQUMvQnlCLEtBQUssSUFBSUM7WUFDVEMsU0FBU0Msb0JBQW9CNUI7UUFDL0I7SUFDRixFQUFFLE9BQU82QixPQUFPO1FBQ2RSLFFBQVFRLEtBQUssQ0FBQyxDQUFDLG9DQUFvQyxFQUFFN0IsT0FBTyxDQUFDLEVBQUU2QjtRQUUvRCxZQUFZO1FBQ1osSUFBSTdCLFdBQVdVLGVBQWU7WUFDNUIsSUFBSTtnQkFDRixNQUFNb0Isa0JBQWtCLENBQUMsTUFBTSx5RUFBTyxHQUFhLEVBQUVwQixjQUFjLE1BQU0sR0FBR0ssT0FBTztnQkFDbkYsT0FBTztvQkFDTEQsVUFBVWdCO29CQUNWUCxVQUFVQyxxQkFBcUJkO29CQUMvQmUsS0FBSyxJQUFJQztvQkFDVEMsU0FBU0Msb0JBQW9CbEI7Z0JBQy9CO1lBQ0YsRUFBRSxPQUFPcUIsY0FBYztnQkFDckJWLFFBQVFRLEtBQUssQ0FBQyxDQUFDLHNDQUFzQyxDQUFDLEVBQUVFO1lBQzFEO1FBQ0Y7UUFFQTVDLHlEQUFRQTtJQUNWO0FBQ0YsRUFBRSxFQUFDO0FBRUgsV0FBVztBQUNYLFNBQVNxQyxxQkFBcUJ4QixNQUFjO0lBQzFDLE1BQU1nQyxjQUFzQztRQUMxQ3hDLElBQUk7UUFDSmEsSUFBSTtRQUNKQyxJQUFJO1FBQ0pDLElBQUk7UUFDSkMsSUFBSTtRQUNKQyxJQUFJO0lBQ047SUFFQSxPQUFPdUIsV0FBVyxDQUFDaEMsT0FBTyxJQUFJO0FBQ2hDO0FBRUEsY0FBYztBQUNkLFNBQVM0QixvQkFBb0I1QixNQUFjO0lBQ3pDLE1BQU1pQyxZQUFpQztRQUNyQ3pDLElBQUk7WUFDRjBDLFVBQVU7Z0JBQ1JDLE9BQU87b0JBQ0xDLEtBQUs7b0JBQ0xDLE9BQU87b0JBQ1BDLE1BQU07Z0JBQ1I7Z0JBQ0FDLE1BQU07b0JBQ0pILEtBQUs7b0JBQ0xDLE9BQU87b0JBQ1BDLE1BQU07b0JBQ05FLFNBQVM7Z0JBQ1g7WUFDRjtZQUNBQyxRQUFRO2dCQUNOeEMsVUFBVTtvQkFDUnlDLE9BQU87b0JBQ1B6QyxVQUFVO2dCQUNaO1lBQ0Y7UUFDRjtRQUNBSSxJQUFJO1lBQ0Y2QixVQUFVO2dCQUNSQyxPQUFPO29CQUNMQyxLQUFLO29CQUNMQyxPQUFPO29CQUNQQyxNQUFNO2dCQUNSO2dCQUNBQyxNQUFNO29CQUNKSCxLQUFLO29CQUNMQyxPQUFPO29CQUNQQyxNQUFNO29CQUNORSxTQUFTO2dCQUNYO1lBQ0Y7WUFDQUMsUUFBUTtnQkFDTnhDLFVBQVU7b0JBQ1J5QyxPQUFPO29CQUNQekMsVUFBVTtnQkFDWjtZQUNGO1FBQ0Y7UUFDQUssSUFBSTtZQUNGNEIsVUFBVTtnQkFDUkMsT0FBTztvQkFDTEMsS0FBSztvQkFDTEMsT0FBTztvQkFDUEMsTUFBTTtnQkFDUjtnQkFDQUMsTUFBTTtvQkFDSkgsS0FBSztvQkFDTEMsT0FBTztvQkFDUEMsTUFBTTtvQkFDTkUsU0FBUztnQkFDWDtZQUNGO1lBQ0FDLFFBQVE7Z0JBQ054QyxVQUFVO29CQUNSeUMsT0FBTztvQkFDUHpDLFVBQVU7Z0JBQ1o7WUFDRjtRQUNGO1FBQ0FNLElBQUk7WUFDRjJCLFVBQVU7Z0JBQ1JDLE9BQU87b0JBQ0xDLEtBQUs7b0JBQ0xDLE9BQU87b0JBQ1BDLE1BQU07Z0JBQ1I7Z0JBQ0FDLE1BQU07b0JBQ0pILEtBQUs7b0JBQ0xDLE9BQU87b0JBQ1BDLE1BQU07b0JBQ05FLFNBQVM7Z0JBQ1g7WUFDRjtZQUNBQyxRQUFRO2dCQUNOeEMsVUFBVTtvQkFDUnlDLE9BQU87b0JBQ1B6QyxVQUFVO2dCQUNaO1lBQ0Y7UUFDRjtRQUNBTyxJQUFJO1lBQ0YwQixVQUFVO2dCQUNSQyxPQUFPO29CQUNMQyxLQUFLO29CQUNMQyxPQUFPO29CQUNQQyxNQUFNO2dCQUNSO2dCQUNBQyxNQUFNO29CQUNKSCxLQUFLO29CQUNMQyxPQUFPO29CQUNQQyxNQUFNO29CQUNORSxTQUFTO2dCQUNYO1lBQ0Y7WUFDQUMsUUFBUTtnQkFDTnhDLFVBQVU7b0JBQ1J5QyxPQUFPO29CQUNQekMsVUFBVTtnQkFDWjtZQUNGO1FBQ0Y7UUFDQVEsSUFBSTtZQUNGeUIsVUFBVTtnQkFDUkMsT0FBTztvQkFDTEMsS0FBSztvQkFDTEMsT0FBTztvQkFDUEMsTUFBTTtnQkFDUjtnQkFDQUMsTUFBTTtvQkFDSkgsS0FBSztvQkFDTEMsT0FBTztvQkFDUEMsTUFBTTtvQkFDTkUsU0FBUztnQkFDWDtZQUNGO1lBQ0FDLFFBQVE7Z0JBQ054QyxVQUFVO29CQUNSeUMsT0FBTztvQkFDUHpDLFVBQVU7Z0JBQ1o7WUFDRjtRQUNGO0lBQ0Y7SUFFQSxPQUFPZ0MsU0FBUyxDQUFDakMsT0FBTyxJQUFJaUMsU0FBUyxDQUFDdkIsY0FBYztBQUN0RDtBQUVBLFNBQVM7QUFDRixNQUFNaUMsWUFBWTtJQUN2QixLQUFLO0lBQ0wsU0FBUztRQUNQbkQsSUFBSTtRQUNKYSxJQUFJO1FBQ0pDLElBQUk7UUFDSkMsSUFBSTtRQUNKQyxJQUFJO1FBQ0pDLElBQUk7SUFDTjtJQUNBLFVBQVU7UUFDUmpCLElBQUk7UUFDSmEsSUFBSTtRQUNKQyxJQUFJO1FBQ0pDLElBQUk7UUFDSkMsSUFBSTtRQUNKQyxJQUFJO0lBQ047SUFDQSxjQUFjO1FBQ1pqQixJQUFJO1FBQ0phLElBQUk7UUFDSkMsSUFBSTtRQUNKQyxJQUFJO1FBQ0pDLElBQUk7UUFDSkMsSUFBSTtJQUNOO0lBQ0EsZUFBZTtRQUNiakIsSUFBSTtRQUNKYSxJQUFJO1FBQ0pDLElBQUk7UUFDSkMsSUFBSTtRQUNKQyxJQUFJO1FBQ0pDLElBQUk7SUFDTjtBQUNGLEVBQVc7QUFFWCxhQUFhO0FBQ04sTUFBTW1DLGVBQWU7SUFDMUJwRCxJQUFJO1FBQ0ZxRCxZQUFZO1FBQ1pDLFlBQVk7UUFDWjdDLFVBQVU7UUFDVjhDLGNBQWM7SUFDaEI7SUFDQTFDLElBQUk7UUFDRndDLFlBQVk7UUFDWkMsWUFBWTtRQUNaN0MsVUFBVTtRQUNWOEMsY0FBYztJQUNoQjtJQUNBekMsSUFBSTtRQUNGdUMsWUFBWTtRQUNaQyxZQUFZO1FBQ1o3QyxVQUFVO1FBQ1Y4QyxjQUFjO0lBQ2hCO0lBQ0F4QyxJQUFJO1FBQ0ZzQyxZQUFZO1FBQ1pDLFlBQVk7UUFDWjdDLFVBQVU7UUFDVjhDLGNBQWM7SUFDaEI7SUFDQXZDLElBQUk7UUFDRnFDLFlBQVk7UUFDWkMsWUFBWTtRQUNaN0MsVUFBVTtRQUNWOEMsY0FBYztJQUNoQjtJQUNBdEMsSUFBSTtRQUNGb0MsWUFBWTtRQUNaQyxZQUFZO1FBQ1o3QyxVQUFVO1FBQ1Y4QyxjQUFjO0lBQ2hCO0FBQ0YsRUFBVyIsInNvdXJjZXMiOlsid2VicGFjazovL215c3RpY2FsLXdlYnNpdGUvLi9zcmMvaTE4bi50cz9iY2Q5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IG5vdEZvdW5kIH0gZnJvbSAnbmV4dC9uYXZpZ2F0aW9uJztcbmltcG9ydCB7IGdldFJlcXVlc3RDb25maWcgfSBmcm9tICduZXh0LWludGwvc2VydmVyJztcblxuLy8g56ys5LiA6Zi25q6177ya5qC45b+D5biC5Zy66K+t6KiAICjnq4vljbPlrp7mlr0pXG5leHBvcnQgY29uc3QgbG9jYWxlcyA9IFsnZW4nLCAnemgnLCAnZXMnLCAncHQnLCAnaGknLCAnamEnXSBhcyBjb25zdDtcbmV4cG9ydCB0eXBlIExvY2FsZSA9ICh0eXBlb2YgbG9jYWxlcylbbnVtYmVyXTtcblxuLy8g56ys5LqM6Zi25q6177ya6YeN6KaB5Yy65Z+f6K+t6KiAICg25Liq5pyI5ZCO5omp5bGVKVxuZXhwb3J0IGNvbnN0IHBoYXNlMkxvY2FsZXMgPSBbJ2RlJywgJ2ZyJywgJ2l0JywgJ3J1JywgJ2tvJywgJ2FyJ10gYXMgY29uc3Q7XG5leHBvcnQgdHlwZSBQaGFzZTJMb2NhbGUgPSAodHlwZW9mIHBoYXNlMkxvY2FsZXMpW251bWJlcl07XG5cbi8vIOaJgOacieaUr+aMgeeahOivreiogOexu+Wei1xuZXhwb3J0IHR5cGUgQWxsTG9jYWxlID0gTG9jYWxlIHwgUGhhc2UyTG9jYWxlO1xuXG4vLyDor63oqIDphY3nva4gLSDljIXlkKvlrozmlbTnmoTlpJror63oqIDlhYPmlbDmja5cbmV4cG9ydCBjb25zdCBsYW5ndWFnZUNvbmZpZyA9IHtcbiAgZW46IHtcbiAgICBuYW1lOiAnRW5nbGlzaCcsXG4gICAgbmF0aXZlTmFtZTogJ0VuZ2xpc2gnLFxuICAgIGRpcmVjdGlvbjogJ2x0cicgYXMgY29uc3QsXG4gICAgcmVnaW9uOiAnR2xvYmFsJyxcbiAgICBwb3B1bGF0aW9uOiAnR2xvYmFsIG1hcmtldCcsXG4gICAgZmxhZzogJ/Cfh7rwn4e4JyxcbiAgICBocmVmbGFuZzogJ2VuJyxcbiAgICBsb2NhbGU6ICdlbi1VUycsXG4gICAgY3VycmVuY3k6ICdVU0QnLFxuICAgIGZvbnRGYW1pbHk6ICdsYXRpbicsXG4gICAgZXhwYW5zaW9uRmFjdG9yOiAxLjAsXG4gICAgcHJpb3JpdHk6IDEsXG4gIH0sXG4gIHpoOiB7XG4gICAgbmFtZTogJ0NoaW5lc2UnLFxuICAgIG5hdGl2ZU5hbWU6ICfkuK3mlocnLFxuICAgIGRpcmVjdGlvbjogJ2x0cicgYXMgY29uc3QsXG4gICAgcmVnaW9uOiAnQ2hpbmEgJiBDaGluZXNlIGNvbW11bml0aWVzJyxcbiAgICBwb3B1bGF0aW9uOiAnMS40IGJpbGxpb24nLFxuICAgIGZsYWc6ICfwn4eo8J+HsycsXG4gICAgaHJlZmxhbmc6ICd6aC1DTicsXG4gICAgbG9jYWxlOiAnemgtQ04nLFxuICAgIGN1cnJlbmN5OiAnQ05ZJyxcbiAgICBmb250RmFtaWx5OiAnY2hpbmVzZScsXG4gICAgZXhwYW5zaW9uRmFjdG9yOiAwLjgsXG4gICAgcHJpb3JpdHk6IDIsXG4gIH0sXG4gIGVzOiB7XG4gICAgbmFtZTogJ1NwYW5pc2gnLFxuICAgIG5hdGl2ZU5hbWU6ICdFc3Bhw7FvbCcsXG4gICAgZGlyZWN0aW9uOiAnbHRyJyBhcyBjb25zdCxcbiAgICByZWdpb246ICdTcGFpbiAmIExhdGluIEFtZXJpY2EnLFxuICAgIHBvcHVsYXRpb246ICc1MDAgbWlsbGlvbicsXG4gICAgZmxhZzogJ/Cfh6rwn4e4JyxcbiAgICBocmVmbGFuZzogJ2VzJyxcbiAgICBsb2NhbGU6ICdlcy1FUycsXG4gICAgY3VycmVuY3k6ICdFVVInLFxuICAgIGZvbnRGYW1pbHk6ICdsYXRpbicsXG4gICAgZXhwYW5zaW9uRmFjdG9yOiAxLjI1LFxuICAgIHByaW9yaXR5OiAzLFxuICB9LFxuICBwdDoge1xuICAgIG5hbWU6ICdQb3J0dWd1ZXNlJyxcbiAgICBuYXRpdmVOYW1lOiAnUG9ydHVndcOqcycsXG4gICAgZGlyZWN0aW9uOiAnbHRyJyBhcyBjb25zdCxcbiAgICByZWdpb246ICdCcmF6aWwgJiBQb3J0dWd1ZXNlLXNwZWFraW5nIGNvdW50cmllcycsXG4gICAgcG9wdWxhdGlvbjogJzI2MCBtaWxsaW9uJyxcbiAgICBmbGFnOiAn8J+Hp/Cfh7cnLFxuICAgIGhyZWZsYW5nOiAncHQtQlInLFxuICAgIGxvY2FsZTogJ3B0LUJSJyxcbiAgICBjdXJyZW5jeTogJ0JSTCcsXG4gICAgZm9udEZhbWlseTogJ2xhdGluJyxcbiAgICBleHBhbnNpb25GYWN0b3I6IDEuMjAsXG4gICAgcHJpb3JpdHk6IDQsXG4gIH0sXG4gIGhpOiB7XG4gICAgbmFtZTogJ0hpbmRpJyxcbiAgICBuYXRpdmVOYW1lOiAn4KS54KS/4KSo4KWN4KSm4KWAJyxcbiAgICBkaXJlY3Rpb246ICdsdHInIGFzIGNvbnN0LFxuICAgIHJlZ2lvbjogJ05vcnRoZXJuIEluZGlhJyxcbiAgICBwb3B1bGF0aW9uOiAnNjAwIG1pbGxpb24nLFxuICAgIGZsYWc6ICfwn4eu8J+HsycsXG4gICAgaHJlZmxhbmc6ICdoaS1JTicsXG4gICAgbG9jYWxlOiAnaGktSU4nLFxuICAgIGN1cnJlbmN5OiAnSU5SJyxcbiAgICBmb250RmFtaWx5OiAnaGluZGknLFxuICAgIGV4cGFuc2lvbkZhY3RvcjogMS40MCxcbiAgICBwcmlvcml0eTogNSxcbiAgfSxcbiAgamE6IHtcbiAgICBuYW1lOiAnSmFwYW5lc2UnLFxuICAgIG5hdGl2ZU5hbWU6ICfml6XmnKzoqp4nLFxuICAgIGRpcmVjdGlvbjogJ2x0cicgYXMgY29uc3QsXG4gICAgcmVnaW9uOiAnSmFwYW4nLFxuICAgIHBvcHVsYXRpb246ICcxMjUgbWlsbGlvbicsXG4gICAgZmxhZzogJ/Cfh6/wn4e1JyxcbiAgICBocmVmbGFuZzogJ2phLUpQJyxcbiAgICBsb2NhbGU6ICdqYS1KUCcsXG4gICAgY3VycmVuY3k6ICdKUFknLFxuICAgIGZvbnRGYW1pbHk6ICdqYXBhbmVzZScsXG4gICAgZXhwYW5zaW9uRmFjdG9yOiAxLjEwLFxuICAgIHByaW9yaXR5OiA2LFxuICB9LFxufSBhcyBjb25zdDtcblxuLy8g6buY6K6k6K+t6KiAXG5leHBvcnQgY29uc3QgZGVmYXVsdExvY2FsZTogTG9jYWxlID0gJ2VuJztcblxuLy8gUlRM6K+t6KiA6YWN572uXG5leHBvcnQgY29uc3QgcnRsTG9jYWxlczogcmVhZG9ubHkgc3RyaW5nW10gPSBbJ2FyJywgJ2hlJywgJ2ZhJywgJ3VyJ10gYXMgY29uc3Q7XG5cbi8vIOivreiogOWbnumAgOmTvumFjee9rlxuZXhwb3J0IGNvbnN0IGZhbGxiYWNrQ2hhaW46IFJlY29yZDxMb2NhbGUsIExvY2FsZVtdPiA9IHtcbiAgZW46IFtdLFxuICB6aDogWydlbiddLFxuICBlczogWydlbiddLFxuICBwdDogWydlcycsICdlbiddLFxuICBoaTogWydlbiddLFxuICBqYTogWydlbiddLFxufTtcblxuLy8g6K+t6KiA5qOA5rWL6YWN572uIC0g5aKe5by654mI5pys77yM5pSv5oyB5Zue6YCA5py65Yi2XG5leHBvcnQgZGVmYXVsdCBnZXRSZXF1ZXN0Q29uZmlnKGFzeW5jICh7IGxvY2FsZSB9KSA9PiB7XG4gIC8vIOmqjOivgeS8oOWFpeeahOivreiogOaYr+WQpuaUr+aMgVxuICBpZiAoIWxvY2FsZXMuaW5jbHVkZXMobG9jYWxlIGFzIExvY2FsZSkpIHtcbiAgICBub3RGb3VuZCgpO1xuICB9XG5cbiAgdHJ5IHtcbiAgICAvLyDlsJ3or5XliqDovb3kuLvopoHor63oqIDmlofku7ZcbiAgICBjb25zdCBtZXNzYWdlcyA9IChhd2FpdCBpbXBvcnQoYC4uL21lc3NhZ2VzLyR7bG9jYWxlfS5qc29uYCkpLmRlZmF1bHQ7XG5cbiAgICAvLyDlpoLmnpzmnInlm57pgIDor63oqIDvvIzlkIjlubbmtojmga9cbiAgICBjb25zdCBmYWxsYmFja3MgPSBmYWxsYmFja0NoYWluW2xvY2FsZSBhcyBMb2NhbGVdIHx8IFtdO1xuICAgIGxldCBtZXJnZWRNZXNzYWdlcyA9IG1lc3NhZ2VzO1xuXG4gICAgZm9yIChjb25zdCBmYWxsYmFja0xvY2FsZSBvZiBmYWxsYmFja3MpIHtcbiAgICAgIHRyeSB7XG4gICAgICAgIGNvbnN0IGZhbGxiYWNrTWVzc2FnZXMgPSAoYXdhaXQgaW1wb3J0KGAuLi9tZXNzYWdlcy8ke2ZhbGxiYWNrTG9jYWxlfS5qc29uYCkpLmRlZmF1bHQ7XG4gICAgICAgIG1lcmdlZE1lc3NhZ2VzID0geyAuLi5mYWxsYmFja01lc3NhZ2VzLCAuLi5tZXJnZWRNZXNzYWdlcyB9O1xuICAgICAgfSBjYXRjaCAoZmFsbGJhY2tFcnJvcikge1xuICAgICAgICBjb25zb2xlLndhcm4oYEZhaWxlZCB0byBsb2FkIGZhbGxiYWNrIG1lc3NhZ2VzIGZvciBsb2NhbGU6ICR7ZmFsbGJhY2tMb2NhbGV9YCwgZmFsbGJhY2tFcnJvcik7XG4gICAgICB9XG4gICAgfVxuXG4gICAgcmV0dXJuIHtcbiAgICAgIG1lc3NhZ2VzOiBtZXJnZWRNZXNzYWdlcyxcbiAgICAgIHRpbWVab25lOiBnZXRUaW1lWm9uZUZvckxvY2FsZShsb2NhbGUgYXMgTG9jYWxlKSxcbiAgICAgIG5vdzogbmV3IERhdGUoKSxcbiAgICAgIGZvcm1hdHM6IGdldEZvcm1hdHNGb3JMb2NhbGUobG9jYWxlIGFzIExvY2FsZSksXG4gICAgfTtcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKGBGYWlsZWQgdG8gbG9hZCBtZXNzYWdlcyBmb3IgbG9jYWxlOiAke2xvY2FsZX1gLCBlcnJvcik7XG5cbiAgICAvLyDlsJ3or5Xlm57pgIDliLDpu5jorqTor63oqIBcbiAgICBpZiAobG9jYWxlICE9PSBkZWZhdWx0TG9jYWxlKSB7XG4gICAgICB0cnkge1xuICAgICAgICBjb25zdCBkZWZhdWx0TWVzc2FnZXMgPSAoYXdhaXQgaW1wb3J0KGAuLi9tZXNzYWdlcy8ke2RlZmF1bHRMb2NhbGV9Lmpzb25gKSkuZGVmYXVsdDtcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICBtZXNzYWdlczogZGVmYXVsdE1lc3NhZ2VzLFxuICAgICAgICAgIHRpbWVab25lOiBnZXRUaW1lWm9uZUZvckxvY2FsZShkZWZhdWx0TG9jYWxlKSxcbiAgICAgICAgICBub3c6IG5ldyBEYXRlKCksXG4gICAgICAgICAgZm9ybWF0czogZ2V0Rm9ybWF0c0ZvckxvY2FsZShkZWZhdWx0TG9jYWxlKSxcbiAgICAgICAgfTtcbiAgICAgIH0gY2F0Y2ggKGRlZmF1bHRFcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKGBGYWlsZWQgdG8gbG9hZCBkZWZhdWx0IGxvY2FsZSBtZXNzYWdlc2AsIGRlZmF1bHRFcnJvcik7XG4gICAgICB9XG4gICAgfVxuXG4gICAgbm90Rm91bmQoKTtcbiAgfVxufSk7XG5cbi8vIOagueaNruivreiogOiOt+WPluaXtuWMulxuZnVuY3Rpb24gZ2V0VGltZVpvbmVGb3JMb2NhbGUobG9jYWxlOiBMb2NhbGUpOiBzdHJpbmcge1xuICBjb25zdCB0aW1lem9uZU1hcDogUmVjb3JkPExvY2FsZSwgc3RyaW5nPiA9IHtcbiAgICBlbjogJ0FtZXJpY2EvTmV3X1lvcmsnLFxuICAgIHpoOiAnQXNpYS9TaGFuZ2hhaScsXG4gICAgZXM6ICdFdXJvcGUvTWFkcmlkJyxcbiAgICBwdDogJ0FtZXJpY2EvU2FvX1BhdWxvJyxcbiAgICBoaTogJ0FzaWEvS29sa2F0YScsXG4gICAgamE6ICdBc2lhL1Rva3lvJyxcbiAgfTtcblxuICByZXR1cm4gdGltZXpvbmVNYXBbbG9jYWxlXSB8fCAnVVRDJztcbn1cblxuLy8g5qC55o2u6K+t6KiA6I635Y+W5qC85byP5YyW6YWN572uXG5mdW5jdGlvbiBnZXRGb3JtYXRzRm9yTG9jYWxlKGxvY2FsZTogTG9jYWxlKSB7XG4gIGNvbnN0IGZvcm1hdE1hcDogUmVjb3JkPExvY2FsZSwgYW55PiA9IHtcbiAgICBlbjoge1xuICAgICAgZGF0ZVRpbWU6IHtcbiAgICAgICAgc2hvcnQ6IHtcbiAgICAgICAgICBkYXk6ICdudW1lcmljJyxcbiAgICAgICAgICBtb250aDogJ3Nob3J0JyxcbiAgICAgICAgICB5ZWFyOiAnbnVtZXJpYydcbiAgICAgICAgfSxcbiAgICAgICAgbG9uZzoge1xuICAgICAgICAgIGRheTogJ251bWVyaWMnLFxuICAgICAgICAgIG1vbnRoOiAnbG9uZycsXG4gICAgICAgICAgeWVhcjogJ251bWVyaWMnLFxuICAgICAgICAgIHdlZWtkYXk6ICdsb25nJ1xuICAgICAgICB9XG4gICAgICB9LFxuICAgICAgbnVtYmVyOiB7XG4gICAgICAgIGN1cnJlbmN5OiB7XG4gICAgICAgICAgc3R5bGU6ICdjdXJyZW5jeScsXG4gICAgICAgICAgY3VycmVuY3k6ICdVU0QnXG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9LFxuICAgIHpoOiB7XG4gICAgICBkYXRlVGltZToge1xuICAgICAgICBzaG9ydDoge1xuICAgICAgICAgIGRheTogJ251bWVyaWMnLFxuICAgICAgICAgIG1vbnRoOiAnc2hvcnQnLFxuICAgICAgICAgIHllYXI6ICdudW1lcmljJ1xuICAgICAgICB9LFxuICAgICAgICBsb25nOiB7XG4gICAgICAgICAgZGF5OiAnbnVtZXJpYycsXG4gICAgICAgICAgbW9udGg6ICdsb25nJyxcbiAgICAgICAgICB5ZWFyOiAnbnVtZXJpYycsXG4gICAgICAgICAgd2Vla2RheTogJ2xvbmcnXG4gICAgICAgIH1cbiAgICAgIH0sXG4gICAgICBudW1iZXI6IHtcbiAgICAgICAgY3VycmVuY3k6IHtcbiAgICAgICAgICBzdHlsZTogJ2N1cnJlbmN5JyxcbiAgICAgICAgICBjdXJyZW5jeTogJ0NOWSdcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH0sXG4gICAgZXM6IHtcbiAgICAgIGRhdGVUaW1lOiB7XG4gICAgICAgIHNob3J0OiB7XG4gICAgICAgICAgZGF5OiAnbnVtZXJpYycsXG4gICAgICAgICAgbW9udGg6ICdzaG9ydCcsXG4gICAgICAgICAgeWVhcjogJ251bWVyaWMnXG4gICAgICAgIH0sXG4gICAgICAgIGxvbmc6IHtcbiAgICAgICAgICBkYXk6ICdudW1lcmljJyxcbiAgICAgICAgICBtb250aDogJ2xvbmcnLFxuICAgICAgICAgIHllYXI6ICdudW1lcmljJyxcbiAgICAgICAgICB3ZWVrZGF5OiAnbG9uZydcbiAgICAgICAgfVxuICAgICAgfSxcbiAgICAgIG51bWJlcjoge1xuICAgICAgICBjdXJyZW5jeToge1xuICAgICAgICAgIHN0eWxlOiAnY3VycmVuY3knLFxuICAgICAgICAgIGN1cnJlbmN5OiAnRVVSJ1xuICAgICAgICB9XG4gICAgICB9XG4gICAgfSxcbiAgICBwdDoge1xuICAgICAgZGF0ZVRpbWU6IHtcbiAgICAgICAgc2hvcnQ6IHtcbiAgICAgICAgICBkYXk6ICdudW1lcmljJyxcbiAgICAgICAgICBtb250aDogJ3Nob3J0JyxcbiAgICAgICAgICB5ZWFyOiAnbnVtZXJpYydcbiAgICAgICAgfSxcbiAgICAgICAgbG9uZzoge1xuICAgICAgICAgIGRheTogJ251bWVyaWMnLFxuICAgICAgICAgIG1vbnRoOiAnbG9uZycsXG4gICAgICAgICAgeWVhcjogJ251bWVyaWMnLFxuICAgICAgICAgIHdlZWtkYXk6ICdsb25nJ1xuICAgICAgICB9XG4gICAgICB9LFxuICAgICAgbnVtYmVyOiB7XG4gICAgICAgIGN1cnJlbmN5OiB7XG4gICAgICAgICAgc3R5bGU6ICdjdXJyZW5jeScsXG4gICAgICAgICAgY3VycmVuY3k6ICdCUkwnXG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9LFxuICAgIGhpOiB7XG4gICAgICBkYXRlVGltZToge1xuICAgICAgICBzaG9ydDoge1xuICAgICAgICAgIGRheTogJ251bWVyaWMnLFxuICAgICAgICAgIG1vbnRoOiAnc2hvcnQnLFxuICAgICAgICAgIHllYXI6ICdudW1lcmljJ1xuICAgICAgICB9LFxuICAgICAgICBsb25nOiB7XG4gICAgICAgICAgZGF5OiAnbnVtZXJpYycsXG4gICAgICAgICAgbW9udGg6ICdsb25nJyxcbiAgICAgICAgICB5ZWFyOiAnbnVtZXJpYycsXG4gICAgICAgICAgd2Vla2RheTogJ2xvbmcnXG4gICAgICAgIH1cbiAgICAgIH0sXG4gICAgICBudW1iZXI6IHtcbiAgICAgICAgY3VycmVuY3k6IHtcbiAgICAgICAgICBzdHlsZTogJ2N1cnJlbmN5JyxcbiAgICAgICAgICBjdXJyZW5jeTogJ0lOUidcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH0sXG4gICAgamE6IHtcbiAgICAgIGRhdGVUaW1lOiB7XG4gICAgICAgIHNob3J0OiB7XG4gICAgICAgICAgZGF5OiAnbnVtZXJpYycsXG4gICAgICAgICAgbW9udGg6ICdzaG9ydCcsXG4gICAgICAgICAgeWVhcjogJ251bWVyaWMnXG4gICAgICAgIH0sXG4gICAgICAgIGxvbmc6IHtcbiAgICAgICAgICBkYXk6ICdudW1lcmljJyxcbiAgICAgICAgICBtb250aDogJ2xvbmcnLFxuICAgICAgICAgIHllYXI6ICdudW1lcmljJyxcbiAgICAgICAgICB3ZWVrZGF5OiAnbG9uZydcbiAgICAgICAgfVxuICAgICAgfSxcbiAgICAgIG51bWJlcjoge1xuICAgICAgICBjdXJyZW5jeToge1xuICAgICAgICAgIHN0eWxlOiAnY3VycmVuY3knLFxuICAgICAgICAgIGN1cnJlbmN5OiAnSlBZJ1xuICAgICAgICB9XG4gICAgICB9XG4gICAgfVxuICB9O1xuXG4gIHJldHVybiBmb3JtYXRNYXBbbG9jYWxlXSB8fCBmb3JtYXRNYXBbZGVmYXVsdExvY2FsZV07XG59XG5cbi8vIOivreiogOi3r+W+hOmFjee9rlxuZXhwb3J0IGNvbnN0IHBhdGhuYW1lcyA9IHtcbiAgJy8nOiAnLycsXG4gICcvYmxvZyc6IHtcbiAgICBlbjogJy9ibG9nJyxcbiAgICB6aDogJy9ibG9nJyxcbiAgICBlczogJy9ibG9nJyxcbiAgICBwdDogJy9ibG9nJyxcbiAgICBoaTogJy9ibG9nJyxcbiAgICBqYTogJy9ibG9nJyxcbiAgfSxcbiAgJy90YXJvdCc6IHtcbiAgICBlbjogJy90YXJvdCcsXG4gICAgemg6ICcvdGFyb3QnLFxuICAgIGVzOiAnL3Rhcm90JyxcbiAgICBwdDogJy90YXJvdCcsXG4gICAgaGk6ICcvdGFyb3QnLFxuICAgIGphOiAnL3Rhcm90JyxcbiAgfSxcbiAgJy9hc3Ryb2xvZ3knOiB7XG4gICAgZW46ICcvYXN0cm9sb2d5JyxcbiAgICB6aDogJy9hc3Ryb2xvZ3knLFxuICAgIGVzOiAnL2FzdHJvbG9neScsXG4gICAgcHQ6ICcvYXN0cm9sb2d5JyxcbiAgICBoaTogJy9hc3Ryb2xvZ3knLFxuICAgIGphOiAnL2FzdHJvbG9neScsXG4gIH0sXG4gICcvbnVtZXJvbG9neSc6IHtcbiAgICBlbjogJy9udW1lcm9sb2d5JyxcbiAgICB6aDogJy9udW1lcm9sb2d5JyxcbiAgICBlczogJy9udW1lcm9sb2d5JyxcbiAgICBwdDogJy9udW1lcm9sb2d5JyxcbiAgICBoaTogJy9udW1lcm9sb2d5JyxcbiAgICBqYTogJy9udW1lcm9sb2d5JyxcbiAgfSxcbn0gYXMgY29uc3Q7XG5cbi8vIOivreiogOeJueWumueahOagvOW8j+WMlumFjee9rlxuZXhwb3J0IGNvbnN0IGZvcm1hdENvbmZpZyA9IHtcbiAgZW46IHtcbiAgICBkYXRlRm9ybWF0OiAnTU0vZGQveXl5eScsXG4gICAgdGltZUZvcm1hdDogJzEyaCcsXG4gICAgY3VycmVuY3k6ICdVU0QnLFxuICAgIG51bWJlckZvcm1hdDogJ2VuLVVTJyxcbiAgfSxcbiAgemg6IHtcbiAgICBkYXRlRm9ybWF0OiAneXl5eeW5tE1N5pyIZGTml6UnLFxuICAgIHRpbWVGb3JtYXQ6ICcyNGgnLFxuICAgIGN1cnJlbmN5OiAnQ05ZJyxcbiAgICBudW1iZXJGb3JtYXQ6ICd6aC1DTicsXG4gIH0sXG4gIGVzOiB7XG4gICAgZGF0ZUZvcm1hdDogJ2RkL01NL3l5eXknLFxuICAgIHRpbWVGb3JtYXQ6ICcyNGgnLFxuICAgIGN1cnJlbmN5OiAnRVVSJyxcbiAgICBudW1iZXJGb3JtYXQ6ICdlcy1FUycsXG4gIH0sXG4gIHB0OiB7XG4gICAgZGF0ZUZvcm1hdDogJ2RkL01NL3l5eXknLFxuICAgIHRpbWVGb3JtYXQ6ICcyNGgnLFxuICAgIGN1cnJlbmN5OiAnQlJMJyxcbiAgICBudW1iZXJGb3JtYXQ6ICdwdC1CUicsXG4gIH0sXG4gIGhpOiB7XG4gICAgZGF0ZUZvcm1hdDogJ2RkL01NL3l5eXknLFxuICAgIHRpbWVGb3JtYXQ6ICcxMmgnLFxuICAgIGN1cnJlbmN5OiAnSU5SJyxcbiAgICBudW1iZXJGb3JtYXQ6ICdoaS1JTicsXG4gIH0sXG4gIGphOiB7XG4gICAgZGF0ZUZvcm1hdDogJ3l5eXnlubRNTeaciGRk5pelJyxcbiAgICB0aW1lRm9ybWF0OiAnMjRoJyxcbiAgICBjdXJyZW5jeTogJ0pQWScsXG4gICAgbnVtYmVyRm9ybWF0OiAnamEtSlAnLFxuICB9LFxufSBhcyBjb25zdDtcbiJdLCJuYW1lcyI6WyJub3RGb3VuZCIsImdldFJlcXVlc3RDb25maWciLCJsb2NhbGVzIiwicGhhc2UyTG9jYWxlcyIsImxhbmd1YWdlQ29uZmlnIiwiZW4iLCJuYW1lIiwibmF0aXZlTmFtZSIsImRpcmVjdGlvbiIsInJlZ2lvbiIsInBvcHVsYXRpb24iLCJmbGFnIiwiaHJlZmxhbmciLCJsb2NhbGUiLCJjdXJyZW5jeSIsImZvbnRGYW1pbHkiLCJleHBhbnNpb25GYWN0b3IiLCJwcmlvcml0eSIsInpoIiwiZXMiLCJwdCIsImhpIiwiamEiLCJkZWZhdWx0TG9jYWxlIiwicnRsTG9jYWxlcyIsImZhbGxiYWNrQ2hhaW4iLCJpbmNsdWRlcyIsIm1lc3NhZ2VzIiwiZGVmYXVsdCIsImZhbGxiYWNrcyIsIm1lcmdlZE1lc3NhZ2VzIiwiZmFsbGJhY2tMb2NhbGUiLCJmYWxsYmFja01lc3NhZ2VzIiwiZmFsbGJhY2tFcnJvciIsImNvbnNvbGUiLCJ3YXJuIiwidGltZVpvbmUiLCJnZXRUaW1lWm9uZUZvckxvY2FsZSIsIm5vdyIsIkRhdGUiLCJmb3JtYXRzIiwiZ2V0Rm9ybWF0c0ZvckxvY2FsZSIsImVycm9yIiwiZGVmYXVsdE1lc3NhZ2VzIiwiZGVmYXVsdEVycm9yIiwidGltZXpvbmVNYXAiLCJmb3JtYXRNYXAiLCJkYXRlVGltZSIsInNob3J0IiwiZGF5IiwibW9udGgiLCJ5ZWFyIiwibG9uZyIsIndlZWtkYXkiLCJudW1iZXIiLCJzdHlsZSIsInBhdGhuYW1lcyIsImZvcm1hdENvbmZpZyIsImRhdGVGb3JtYXQiLCJ0aW1lRm9ybWF0IiwibnVtYmVyRm9ybWF0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/i18n.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/seo.ts":
/*!************************!*\
  !*** ./src/lib/seo.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generateArticleStructuredData: () => (/* binding */ generateArticleStructuredData),\n/* harmony export */   generateBreadcrumbStructuredData: () => (/* binding */ generateBreadcrumbStructuredData),\n/* harmony export */   generateFAQStructuredData: () => (/* binding */ generateFAQStructuredData),\n/* harmony export */   generateMetadata: () => (/* binding */ generateMetadata),\n/* harmony export */   generateOrganizationStructuredData: () => (/* binding */ generateOrganizationStructuredData),\n/* harmony export */   generateStructuredData: () => (/* binding */ generateStructuredData),\n/* harmony export */   generateTestStructuredData: () => (/* binding */ generateTestStructuredData),\n/* harmony export */   generateWebsiteStructuredData: () => (/* binding */ generateWebsiteStructuredData),\n/* harmony export */   optimizeDescription: () => (/* binding */ optimizeDescription),\n/* harmony export */   optimizeTitle: () => (/* binding */ optimizeTitle)\n/* harmony export */ });\n/**\n * 生成页面元数据\n */ function generateMetadata(config) {\n    const { title, description, keywords = [], image, url, type = \"website\", locale = \"en\", siteName = \"Mystical Website\", author, publishedTime, modifiedTime, section, tags = [] } = config;\n    const metadata = {\n        title,\n        description,\n        keywords: keywords.join(\", \"),\n        ...author && {\n            authors: [\n                {\n                    name: author\n                }\n            ]\n        },\n        // Open Graph\n        openGraph: {\n            title,\n            description,\n            type,\n            locale,\n            siteName,\n            ...url && {\n                url\n            },\n            ...image && {\n                images: [\n                    {\n                        url: image,\n                        alt: title\n                    }\n                ]\n            },\n            ...publishedTime && {\n                publishedTime\n            },\n            ...modifiedTime && {\n                modifiedTime\n            },\n            ...section && {\n                section\n            },\n            ...tags && tags.length > 0 && {\n                tags\n            }\n        },\n        // Twitter Card\n        twitter: {\n            card: \"summary_large_image\",\n            title,\n            description,\n            ...image && {\n                images: [\n                    image\n                ]\n            },\n            ...author && {\n                creator: `@${author}`\n            }\n        },\n        // 其他元数据\n        robots: {\n            index: true,\n            follow: true,\n            googleBot: {\n                index: true,\n                follow: true,\n                \"max-video-preview\": -1,\n                \"max-image-preview\": \"large\",\n                \"max-snippet\": -1\n            }\n        },\n        // 验证标签\n        verification: {\n            google: process.env[\"NEXT_PUBLIC_GOOGLE_SITE_VERIFICATION\"] || null,\n            other: {\n                \"msvalidate.01\": process.env[\"NEXT_PUBLIC_BING_SITE_VERIFICATION\"] || \"\"\n            }\n        }\n    };\n    return metadata;\n}\n/**\n * 生成结构化数据 (JSON-LD)\n */ function generateStructuredData(type, data) {\n    const baseStructure = {\n        \"@context\": \"https://schema.org\",\n        \"@type\": type,\n        ...data\n    };\n    return JSON.stringify(baseStructure);\n}\n/**\n * 生成网站结构化数据\n */ function generateWebsiteStructuredData(locale = \"en\") {\n    return generateStructuredData(\"WebSite\", {\n        name: \"Mystical Website\",\n        description: \"Professional mystical platform offering free tarot, astrology, and numerology tests with AI analysis.\",\n        url: \"http://localhost:3000\" || 0,\n        inLanguage: locale,\n        potentialAction: {\n            \"@type\": \"SearchAction\",\n            target: `${\"http://localhost:3000\" || 0}/search?q={search_term_string}`,\n            \"query-input\": \"required name=search_term_string\"\n        }\n    });\n}\n/**\n * 生成文章结构化数据\n */ function generateArticleStructuredData(article) {\n    return generateStructuredData(\"Article\", {\n        headline: article.title,\n        description: article.description,\n        author: {\n            \"@type\": \"Person\",\n            name: article.author\n        },\n        publisher: {\n            \"@type\": \"Organization\",\n            name: \"Mystical Website\",\n            logo: {\n                \"@type\": \"ImageObject\",\n                url: `${\"http://localhost:3000\" || 0}/images/logo.png`\n            }\n        },\n        datePublished: article.publishedTime,\n        dateModified: article.modifiedTime || article.publishedTime,\n        image: article.image,\n        url: article.url,\n        mainEntityOfPage: article.url,\n        articleSection: article.category\n    });\n}\n/**\n * 生成产品结构化数据（用于测试页面）\n */ function generateTestStructuredData(test) {\n    return generateStructuredData(\"Product\", {\n        name: test.name,\n        description: test.description,\n        category: test.category,\n        url: test.url,\n        image: test.image,\n        brand: {\n            \"@type\": \"Brand\",\n            name: \"Mystical Website\"\n        },\n        offers: {\n            \"@type\": \"Offer\",\n            price: \"0\",\n            priceCurrency: \"USD\",\n            availability: \"https://schema.org/InStock\"\n        }\n    });\n}\n/**\n * 生成面包屑结构化数据\n */ function generateBreadcrumbStructuredData(breadcrumbs) {\n    return generateStructuredData(\"BreadcrumbList\", {\n        itemListElement: breadcrumbs.map((item, index)=>({\n                \"@type\": \"ListItem\",\n                position: index + 1,\n                name: item.name,\n                item: item.url\n            }))\n    });\n}\n/**\n * 生成FAQ结构化数据\n */ function generateFAQStructuredData(faqs) {\n    return generateStructuredData(\"FAQPage\", {\n        mainEntity: faqs.map((faq)=>({\n                \"@type\": \"Question\",\n                name: faq.question,\n                acceptedAnswer: {\n                    \"@type\": \"Answer\",\n                    text: faq.answer\n                }\n            }))\n    });\n}\n/**\n * 生成组织结构化数据\n */ function generateOrganizationStructuredData() {\n    return generateStructuredData(\"Organization\", {\n        name: \"Mystical Website\",\n        description: \"Professional mystical platform offering free tarot, astrology, and numerology tests.\",\n        url: \"http://localhost:3000\" || 0,\n        logo: `${\"http://localhost:3000\" || 0}/images/logo.png`,\n        sameAs: [\n            \"https://twitter.com/mystical_website\",\n            \"https://facebook.com/mystical_website\",\n            \"https://instagram.com/mystical_website\"\n        ],\n        contactPoint: {\n            \"@type\": \"ContactPoint\",\n            contactType: \"Customer Service\",\n            email: \"<EMAIL>\"\n        }\n    });\n}\n/**\n * 优化标题长度\n */ function optimizeTitle(title, maxLength = 60) {\n    if (title.length <= maxLength) return title;\n    // 尝试在单词边界截断\n    const truncated = title.slice(0, maxLength);\n    const lastSpace = truncated.lastIndexOf(\" \");\n    if (lastSpace > maxLength * 0.8) {\n        return truncated.slice(0, lastSpace) + \"...\";\n    }\n    return truncated.slice(0, maxLength - 3) + \"...\";\n}\n/**\n * 优化描述长度\n */ function optimizeDescription(description, maxLength = 160) {\n    if (description.length <= maxLength) return description;\n    // 尝试在句子边界截断\n    const sentences = description.split(\". \");\n    let result = \"\";\n    for (const sentence of sentences){\n        if ((result + sentence + \". \").length <= maxLength) {\n            result += sentence + \". \";\n        } else {\n            break;\n        }\n    }\n    if (result.length > 0) {\n        return result.trim();\n    }\n    // 如果没有合适的句子边界，在单词边界截断\n    const truncated = description.slice(0, maxLength);\n    const lastSpace = truncated.lastIndexOf(\" \");\n    if (lastSpace > maxLength * 0.8) {\n        return truncated.slice(0, lastSpace) + \"...\";\n    }\n    return truncated.slice(0, maxLength - 3) + \"...\";\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3Nlby50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7O0FBa0JBOztDQUVDLEdBQ00sU0FBU0EsaUJBQWlCQyxNQUFpQjtJQUNoRCxNQUFNLEVBQ0pDLEtBQUssRUFDTEMsV0FBVyxFQUNYQyxXQUFXLEVBQUUsRUFDYkMsS0FBSyxFQUNMQyxHQUFHLEVBQ0hDLE9BQU8sU0FBUyxFQUNoQkMsU0FBUyxJQUFJLEVBQ2JDLFdBQVcsa0JBQWtCLEVBQzdCQyxNQUFNLEVBQ05DLGFBQWEsRUFDYkMsWUFBWSxFQUNaQyxPQUFPLEVBQ1BDLE9BQU8sRUFBRSxFQUNWLEdBQUdiO0lBRUosTUFBTWMsV0FBcUI7UUFDekJiO1FBQ0FDO1FBQ0FDLFVBQVVBLFNBQVNZLElBQUksQ0FBQztRQUN4QixHQUFJTixVQUFVO1lBQUVPLFNBQVM7Z0JBQUM7b0JBQUVDLE1BQU1SO2dCQUFPO2FBQUU7UUFBQyxDQUFDO1FBRTdDLGFBQWE7UUFDYlMsV0FBVztZQUNUakI7WUFDQUM7WUFDQUk7WUFDQUM7WUFDQUM7WUFDQSxHQUFJSCxPQUFPO2dCQUFFQTtZQUFJLENBQUM7WUFDbEIsR0FBSUQsU0FBUztnQkFBRWUsUUFBUTtvQkFBQzt3QkFBRWQsS0FBS0Q7d0JBQU9nQixLQUFLbkI7b0JBQU07aUJBQUU7WUFBQyxDQUFDO1lBQ3JELEdBQUlTLGlCQUFpQjtnQkFBRUE7WUFBYyxDQUFDO1lBQ3RDLEdBQUlDLGdCQUFnQjtnQkFBRUE7WUFBYSxDQUFDO1lBQ3BDLEdBQUlDLFdBQVc7Z0JBQUVBO1lBQVEsQ0FBQztZQUMxQixHQUFJQyxRQUFRQSxLQUFLUSxNQUFNLEdBQUcsS0FBSztnQkFBRVI7WUFBSyxDQUFDO1FBQ3pDO1FBRUEsZUFBZTtRQUNmUyxTQUFTO1lBQ1BDLE1BQU07WUFDTnRCO1lBQ0FDO1lBQ0EsR0FBSUUsU0FBUztnQkFBRWUsUUFBUTtvQkFBQ2Y7aUJBQU07WUFBQyxDQUFDO1lBQ2hDLEdBQUlLLFVBQVU7Z0JBQUVlLFNBQVMsQ0FBQyxDQUFDLEVBQUVmLE9BQU8sQ0FBQztZQUFDLENBQUM7UUFDekM7UUFFQSxRQUFRO1FBQ1JnQixRQUFRO1lBQ05DLE9BQU87WUFDUEMsUUFBUTtZQUNSQyxXQUFXO2dCQUNURixPQUFPO2dCQUNQQyxRQUFRO2dCQUNSLHFCQUFxQixDQUFDO2dCQUN0QixxQkFBcUI7Z0JBQ3JCLGVBQWUsQ0FBQztZQUNsQjtRQUNGO1FBRUEsT0FBTztRQUNQRSxjQUFjO1lBQ1pDLFFBQVFDLFFBQVFDLEdBQUcsQ0FBQyx1Q0FBdUMsSUFBSTtZQUMvREMsT0FBTztnQkFDTCxpQkFBaUJGLFFBQVFDLEdBQUcsQ0FBQyxxQ0FBcUMsSUFBSTtZQUN4RTtRQUNGO0lBQ0Y7SUFFQSxPQUFPbEI7QUFDVDtBQUVBOztDQUVDLEdBQ00sU0FBU29CLHVCQUF1QjVCLElBQVksRUFBRTZCLElBQXlCO0lBQzVFLE1BQU1DLGdCQUFnQjtRQUNwQixZQUFZO1FBQ1osU0FBUzlCO1FBQ1QsR0FBRzZCLElBQUk7SUFDVDtJQUVBLE9BQU9FLEtBQUtDLFNBQVMsQ0FBQ0Y7QUFDeEI7QUFFQTs7Q0FFQyxHQUNNLFNBQVNHLDhCQUE4QmhDLFNBQWlCLElBQUk7SUFDakUsT0FBTzJCLHVCQUF1QixXQUFXO1FBQ3ZDakIsTUFBTTtRQUNOZixhQUFhO1FBQ2JHLEtBQUswQix1QkFBa0MsSUFBSTtRQUMzQ1MsWUFBWWpDO1FBQ1prQyxpQkFBaUI7WUFDZixTQUFTO1lBQ1RDLFFBQVEsQ0FBQyxFQUFFWCx1QkFBa0MsSUFBSSxFQUErQiw4QkFBOEIsQ0FBQztZQUMvRyxlQUFlO1FBQ2pCO0lBQ0Y7QUFDRjtBQUVBOztDQUVDLEdBQ00sU0FBU1ksOEJBQThCQyxPQVM3QztJQUNDLE9BQU9WLHVCQUF1QixXQUFXO1FBQ3ZDVyxVQUFVRCxRQUFRM0MsS0FBSztRQUN2QkMsYUFBYTBDLFFBQVExQyxXQUFXO1FBQ2hDTyxRQUFRO1lBQ04sU0FBUztZQUNUUSxNQUFNMkIsUUFBUW5DLE1BQU07UUFDdEI7UUFDQXFDLFdBQVc7WUFDVCxTQUFTO1lBQ1Q3QixNQUFNO1lBQ044QixNQUFNO2dCQUNKLFNBQVM7Z0JBQ1QxQyxLQUFLLENBQUMsRUFBRTBCLHVCQUFrQyxJQUFJLEVBQStCLGdCQUFnQixDQUFDO1lBQ2hHO1FBQ0Y7UUFDQWlCLGVBQWVKLFFBQVFsQyxhQUFhO1FBQ3BDdUMsY0FBY0wsUUFBUWpDLFlBQVksSUFBSWlDLFFBQVFsQyxhQUFhO1FBQzNETixPQUFPd0MsUUFBUXhDLEtBQUs7UUFDcEJDLEtBQUt1QyxRQUFRdkMsR0FBRztRQUNoQjZDLGtCQUFrQk4sUUFBUXZDLEdBQUc7UUFDN0I4QyxnQkFBZ0JQLFFBQVFRLFFBQVE7SUFDbEM7QUFDRjtBQUVBOztDQUVDLEdBQ00sU0FBU0MsMkJBQTJCQyxJQU0xQztJQUNDLE9BQU9wQix1QkFBdUIsV0FBVztRQUN2Q2pCLE1BQU1xQyxLQUFLckMsSUFBSTtRQUNmZixhQUFhb0QsS0FBS3BELFdBQVc7UUFDN0JrRCxVQUFVRSxLQUFLRixRQUFRO1FBQ3ZCL0MsS0FBS2lELEtBQUtqRCxHQUFHO1FBQ2JELE9BQU9rRCxLQUFLbEQsS0FBSztRQUNqQm1ELE9BQU87WUFDTCxTQUFTO1lBQ1R0QyxNQUFNO1FBQ1I7UUFDQXVDLFFBQVE7WUFDTixTQUFTO1lBQ1RDLE9BQU87WUFDUEMsZUFBZTtZQUNmQyxjQUFjO1FBQ2hCO0lBQ0Y7QUFDRjtBQUVBOztDQUVDLEdBQ00sU0FBU0MsaUNBQWlDQyxXQUFpRDtJQUNoRyxPQUFPM0IsdUJBQXVCLGtCQUFrQjtRQUM5QzRCLGlCQUFpQkQsWUFBWUUsR0FBRyxDQUFDLENBQUNDLE1BQU10QyxRQUFXO2dCQUNqRCxTQUFTO2dCQUNUdUMsVUFBVXZDLFFBQVE7Z0JBQ2xCVCxNQUFNK0MsS0FBSy9DLElBQUk7Z0JBQ2YrQyxNQUFNQSxLQUFLM0QsR0FBRztZQUNoQjtJQUNGO0FBQ0Y7QUFFQTs7Q0FFQyxHQUNNLFNBQVM2RCwwQkFBMEJDLElBQWlEO0lBQ3pGLE9BQU9qQyx1QkFBdUIsV0FBVztRQUN2Q2tDLFlBQVlELEtBQUtKLEdBQUcsQ0FBQ00sQ0FBQUEsTUFBUTtnQkFDM0IsU0FBUztnQkFDVHBELE1BQU1vRCxJQUFJQyxRQUFRO2dCQUNsQkMsZ0JBQWdCO29CQUNkLFNBQVM7b0JBQ1RDLE1BQU1ILElBQUlJLE1BQU07Z0JBQ2xCO1lBQ0Y7SUFDRjtBQUNGO0FBRUE7O0NBRUMsR0FDTSxTQUFTQztJQUNkLE9BQU94Qyx1QkFBdUIsZ0JBQWdCO1FBQzVDakIsTUFBTTtRQUNOZixhQUFhO1FBQ2JHLEtBQUswQix1QkFBa0MsSUFBSTtRQUMzQ2dCLE1BQU0sQ0FBQyxFQUFFaEIsdUJBQWtDLElBQUksRUFBK0IsZ0JBQWdCLENBQUM7UUFDL0Y0QyxRQUFRO1lBQ047WUFDQTtZQUNBO1NBQ0Q7UUFDREMsY0FBYztZQUNaLFNBQVM7WUFDVEMsYUFBYTtZQUNiQyxPQUFPO1FBQ1Q7SUFDRjtBQUNGO0FBRUE7O0NBRUMsR0FDTSxTQUFTQyxjQUFjOUUsS0FBYSxFQUFFK0UsWUFBb0IsRUFBRTtJQUNqRSxJQUFJL0UsTUFBTW9CLE1BQU0sSUFBSTJELFdBQVcsT0FBTy9FO0lBRXRDLFlBQVk7SUFDWixNQUFNZ0YsWUFBWWhGLE1BQU1pRixLQUFLLENBQUMsR0FBR0Y7SUFDakMsTUFBTUcsWUFBWUYsVUFBVUcsV0FBVyxDQUFDO0lBRXhDLElBQUlELFlBQVlILFlBQVksS0FBSztRQUMvQixPQUFPQyxVQUFVQyxLQUFLLENBQUMsR0FBR0MsYUFBYTtJQUN6QztJQUVBLE9BQU9GLFVBQVVDLEtBQUssQ0FBQyxHQUFHRixZQUFZLEtBQUs7QUFDN0M7QUFFQTs7Q0FFQyxHQUNNLFNBQVNLLG9CQUFvQm5GLFdBQW1CLEVBQUU4RSxZQUFvQixHQUFHO0lBQzlFLElBQUk5RSxZQUFZbUIsTUFBTSxJQUFJMkQsV0FBVyxPQUFPOUU7SUFFNUMsWUFBWTtJQUNaLE1BQU1vRixZQUFZcEYsWUFBWXFGLEtBQUssQ0FBQztJQUNwQyxJQUFJQyxTQUFTO0lBRWIsS0FBSyxNQUFNQyxZQUFZSCxVQUFXO1FBQ2hDLElBQUksQ0FBQ0UsU0FBU0MsV0FBVyxJQUFHLEVBQUdwRSxNQUFNLElBQUkyRCxXQUFXO1lBQ2xEUSxVQUFVQyxXQUFXO1FBQ3ZCLE9BQU87WUFDTDtRQUNGO0lBQ0Y7SUFFQSxJQUFJRCxPQUFPbkUsTUFBTSxHQUFHLEdBQUc7UUFDckIsT0FBT21FLE9BQU9FLElBQUk7SUFDcEI7SUFFQSxzQkFBc0I7SUFDdEIsTUFBTVQsWUFBWS9FLFlBQVlnRixLQUFLLENBQUMsR0FBR0Y7SUFDdkMsTUFBTUcsWUFBWUYsVUFBVUcsV0FBVyxDQUFDO0lBRXhDLElBQUlELFlBQVlILFlBQVksS0FBSztRQUMvQixPQUFPQyxVQUFVQyxLQUFLLENBQUMsR0FBR0MsYUFBYTtJQUN6QztJQUVBLE9BQU9GLFVBQVVDLEtBQUssQ0FBQyxHQUFHRixZQUFZLEtBQUs7QUFDN0MiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9teXN0aWNhbC13ZWJzaXRlLy4vc3JjL2xpYi9zZW8udHM/YjQ0YiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IE1ldGFkYXRhIH0gZnJvbSAnbmV4dCc7XG5cbmV4cG9ydCBpbnRlcmZhY2UgU0VPQ29uZmlnIHtcbiAgdGl0bGU6IHN0cmluZztcbiAgZGVzY3JpcHRpb246IHN0cmluZztcbiAga2V5d29yZHM/OiBzdHJpbmdbXTtcbiAgaW1hZ2U/OiBzdHJpbmc7XG4gIHVybD86IHN0cmluZztcbiAgdHlwZT86ICd3ZWJzaXRlJyB8ICdhcnRpY2xlJztcbiAgbG9jYWxlPzogc3RyaW5nO1xuICBzaXRlTmFtZT86IHN0cmluZztcbiAgYXV0aG9yPzogc3RyaW5nO1xuICBwdWJsaXNoZWRUaW1lPzogc3RyaW5nO1xuICBtb2RpZmllZFRpbWU/OiBzdHJpbmc7XG4gIHNlY3Rpb24/OiBzdHJpbmc7XG4gIHRhZ3M/OiBzdHJpbmdbXTtcbn1cblxuLyoqXG4gKiDnlJ/miJDpobXpnaLlhYPmlbDmja5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGdlbmVyYXRlTWV0YWRhdGEoY29uZmlnOiBTRU9Db25maWcpOiBNZXRhZGF0YSB7XG4gIGNvbnN0IHtcbiAgICB0aXRsZSxcbiAgICBkZXNjcmlwdGlvbixcbiAgICBrZXl3b3JkcyA9IFtdLFxuICAgIGltYWdlLFxuICAgIHVybCxcbiAgICB0eXBlID0gJ3dlYnNpdGUnLFxuICAgIGxvY2FsZSA9ICdlbicsXG4gICAgc2l0ZU5hbWUgPSAnTXlzdGljYWwgV2Vic2l0ZScsXG4gICAgYXV0aG9yLFxuICAgIHB1Ymxpc2hlZFRpbWUsXG4gICAgbW9kaWZpZWRUaW1lLFxuICAgIHNlY3Rpb24sXG4gICAgdGFncyA9IFtdLFxuICB9ID0gY29uZmlnO1xuXG4gIGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcbiAgICB0aXRsZSxcbiAgICBkZXNjcmlwdGlvbixcbiAgICBrZXl3b3Jkczoga2V5d29yZHMuam9pbignLCAnKSxcbiAgICAuLi4oYXV0aG9yICYmIHsgYXV0aG9yczogW3sgbmFtZTogYXV0aG9yIH1dIH0pLFxuICAgIFxuICAgIC8vIE9wZW4gR3JhcGhcbiAgICBvcGVuR3JhcGg6IHtcbiAgICAgIHRpdGxlLFxuICAgICAgZGVzY3JpcHRpb24sXG4gICAgICB0eXBlLFxuICAgICAgbG9jYWxlLFxuICAgICAgc2l0ZU5hbWUsXG4gICAgICAuLi4odXJsICYmIHsgdXJsIH0pLFxuICAgICAgLi4uKGltYWdlICYmIHsgaW1hZ2VzOiBbeyB1cmw6IGltYWdlLCBhbHQ6IHRpdGxlIH1dIH0pLFxuICAgICAgLi4uKHB1Ymxpc2hlZFRpbWUgJiYgeyBwdWJsaXNoZWRUaW1lIH0pLFxuICAgICAgLi4uKG1vZGlmaWVkVGltZSAmJiB7IG1vZGlmaWVkVGltZSB9KSxcbiAgICAgIC4uLihzZWN0aW9uICYmIHsgc2VjdGlvbiB9KSxcbiAgICAgIC4uLih0YWdzICYmIHRhZ3MubGVuZ3RoID4gMCAmJiB7IHRhZ3MgfSksXG4gICAgfSxcbiAgICBcbiAgICAvLyBUd2l0dGVyIENhcmRcbiAgICB0d2l0dGVyOiB7XG4gICAgICBjYXJkOiAnc3VtbWFyeV9sYXJnZV9pbWFnZScsXG4gICAgICB0aXRsZSxcbiAgICAgIGRlc2NyaXB0aW9uLFxuICAgICAgLi4uKGltYWdlICYmIHsgaW1hZ2VzOiBbaW1hZ2VdIH0pLFxuICAgICAgLi4uKGF1dGhvciAmJiB7IGNyZWF0b3I6IGBAJHthdXRob3J9YCB9KSxcbiAgICB9LFxuICAgIFxuICAgIC8vIOWFtuS7luWFg+aVsOaNrlxuICAgIHJvYm90czoge1xuICAgICAgaW5kZXg6IHRydWUsXG4gICAgICBmb2xsb3c6IHRydWUsXG4gICAgICBnb29nbGVCb3Q6IHtcbiAgICAgICAgaW5kZXg6IHRydWUsXG4gICAgICAgIGZvbGxvdzogdHJ1ZSxcbiAgICAgICAgJ21heC12aWRlby1wcmV2aWV3JzogLTEsXG4gICAgICAgICdtYXgtaW1hZ2UtcHJldmlldyc6ICdsYXJnZScsXG4gICAgICAgICdtYXgtc25pcHBldCc6IC0xLFxuICAgICAgfSxcbiAgICB9LFxuICAgIFxuICAgIC8vIOmqjOivgeagh+etvlxuICAgIHZlcmlmaWNhdGlvbjoge1xuICAgICAgZ29vZ2xlOiBwcm9jZXNzLmVudlsnTkVYVF9QVUJMSUNfR09PR0xFX1NJVEVfVkVSSUZJQ0FUSU9OJ10gfHwgbnVsbCxcbiAgICAgIG90aGVyOiB7XG4gICAgICAgICdtc3ZhbGlkYXRlLjAxJzogcHJvY2Vzcy5lbnZbJ05FWFRfUFVCTElDX0JJTkdfU0lURV9WRVJJRklDQVRJT04nXSB8fCAnJyxcbiAgICAgIH0sXG4gICAgfSxcbiAgfTtcblxuICByZXR1cm4gbWV0YWRhdGE7XG59XG5cbi8qKlxuICog55Sf5oiQ57uT5p6E5YyW5pWw5o2uIChKU09OLUxEKVxuICovXG5leHBvcnQgZnVuY3Rpb24gZ2VuZXJhdGVTdHJ1Y3R1cmVkRGF0YSh0eXBlOiBzdHJpbmcsIGRhdGE6IFJlY29yZDxzdHJpbmcsIGFueT4pIHtcbiAgY29uc3QgYmFzZVN0cnVjdHVyZSA9IHtcbiAgICAnQGNvbnRleHQnOiAnaHR0cHM6Ly9zY2hlbWEub3JnJyxcbiAgICAnQHR5cGUnOiB0eXBlLFxuICAgIC4uLmRhdGEsXG4gIH07XG5cbiAgcmV0dXJuIEpTT04uc3RyaW5naWZ5KGJhc2VTdHJ1Y3R1cmUpO1xufVxuXG4vKipcbiAqIOeUn+aIkOe9keermee7k+aehOWMluaVsOaNrlxuICovXG5leHBvcnQgZnVuY3Rpb24gZ2VuZXJhdGVXZWJzaXRlU3RydWN0dXJlZERhdGEobG9jYWxlOiBzdHJpbmcgPSAnZW4nKSB7XG4gIHJldHVybiBnZW5lcmF0ZVN0cnVjdHVyZWREYXRhKCdXZWJTaXRlJywge1xuICAgIG5hbWU6ICdNeXN0aWNhbCBXZWJzaXRlJyxcbiAgICBkZXNjcmlwdGlvbjogJ1Byb2Zlc3Npb25hbCBteXN0aWNhbCBwbGF0Zm9ybSBvZmZlcmluZyBmcmVlIHRhcm90LCBhc3Ryb2xvZ3ksIGFuZCBudW1lcm9sb2d5IHRlc3RzIHdpdGggQUkgYW5hbHlzaXMuJyxcbiAgICB1cmw6IHByb2Nlc3MuZW52WydORVhUX1BVQkxJQ19BUFBfVVJMJ10gfHwgJ2h0dHBzOi8vbXlzdGljYWwtd2Vic2l0ZS5jb20nLFxuICAgIGluTGFuZ3VhZ2U6IGxvY2FsZSxcbiAgICBwb3RlbnRpYWxBY3Rpb246IHtcbiAgICAgICdAdHlwZSc6ICdTZWFyY2hBY3Rpb24nLFxuICAgICAgdGFyZ2V0OiBgJHtwcm9jZXNzLmVudlsnTkVYVF9QVUJMSUNfQVBQX1VSTCddIHx8ICdodHRwczovL215c3RpY2FsLXdlYnNpdGUuY29tJ30vc2VhcmNoP3E9e3NlYXJjaF90ZXJtX3N0cmluZ31gLFxuICAgICAgJ3F1ZXJ5LWlucHV0JzogJ3JlcXVpcmVkIG5hbWU9c2VhcmNoX3Rlcm1fc3RyaW5nJyxcbiAgICB9LFxuICB9KTtcbn1cblxuLyoqXG4gKiDnlJ/miJDmlofnq6Dnu5PmnoTljJbmlbDmja5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGdlbmVyYXRlQXJ0aWNsZVN0cnVjdHVyZWREYXRhKGFydGljbGU6IHtcbiAgdGl0bGU6IHN0cmluZztcbiAgZGVzY3JpcHRpb246IHN0cmluZztcbiAgYXV0aG9yOiBzdHJpbmc7XG4gIHB1Ymxpc2hlZFRpbWU6IHN0cmluZztcbiAgbW9kaWZpZWRUaW1lPzogc3RyaW5nO1xuICBpbWFnZT86IHN0cmluZztcbiAgdXJsOiBzdHJpbmc7XG4gIGNhdGVnb3J5OiBzdHJpbmc7XG59KSB7XG4gIHJldHVybiBnZW5lcmF0ZVN0cnVjdHVyZWREYXRhKCdBcnRpY2xlJywge1xuICAgIGhlYWRsaW5lOiBhcnRpY2xlLnRpdGxlLFxuICAgIGRlc2NyaXB0aW9uOiBhcnRpY2xlLmRlc2NyaXB0aW9uLFxuICAgIGF1dGhvcjoge1xuICAgICAgJ0B0eXBlJzogJ1BlcnNvbicsXG4gICAgICBuYW1lOiBhcnRpY2xlLmF1dGhvcixcbiAgICB9LFxuICAgIHB1Ymxpc2hlcjoge1xuICAgICAgJ0B0eXBlJzogJ09yZ2FuaXphdGlvbicsXG4gICAgICBuYW1lOiAnTXlzdGljYWwgV2Vic2l0ZScsXG4gICAgICBsb2dvOiB7XG4gICAgICAgICdAdHlwZSc6ICdJbWFnZU9iamVjdCcsXG4gICAgICAgIHVybDogYCR7cHJvY2Vzcy5lbnZbJ05FWFRfUFVCTElDX0FQUF9VUkwnXSB8fCAnaHR0cHM6Ly9teXN0aWNhbC13ZWJzaXRlLmNvbSd9L2ltYWdlcy9sb2dvLnBuZ2AsXG4gICAgICB9LFxuICAgIH0sXG4gICAgZGF0ZVB1Ymxpc2hlZDogYXJ0aWNsZS5wdWJsaXNoZWRUaW1lLFxuICAgIGRhdGVNb2RpZmllZDogYXJ0aWNsZS5tb2RpZmllZFRpbWUgfHwgYXJ0aWNsZS5wdWJsaXNoZWRUaW1lLFxuICAgIGltYWdlOiBhcnRpY2xlLmltYWdlLFxuICAgIHVybDogYXJ0aWNsZS51cmwsXG4gICAgbWFpbkVudGl0eU9mUGFnZTogYXJ0aWNsZS51cmwsXG4gICAgYXJ0aWNsZVNlY3Rpb246IGFydGljbGUuY2F0ZWdvcnksXG4gIH0pO1xufVxuXG4vKipcbiAqIOeUn+aIkOS6p+WTgee7k+aehOWMluaVsOaNru+8iOeUqOS6jua1i+ivlemhtemdou+8iVxuICovXG5leHBvcnQgZnVuY3Rpb24gZ2VuZXJhdGVUZXN0U3RydWN0dXJlZERhdGEodGVzdDoge1xuICBuYW1lOiBzdHJpbmc7XG4gIGRlc2NyaXB0aW9uOiBzdHJpbmc7XG4gIGNhdGVnb3J5OiBzdHJpbmc7XG4gIHVybDogc3RyaW5nO1xuICBpbWFnZT86IHN0cmluZztcbn0pIHtcbiAgcmV0dXJuIGdlbmVyYXRlU3RydWN0dXJlZERhdGEoJ1Byb2R1Y3QnLCB7XG4gICAgbmFtZTogdGVzdC5uYW1lLFxuICAgIGRlc2NyaXB0aW9uOiB0ZXN0LmRlc2NyaXB0aW9uLFxuICAgIGNhdGVnb3J5OiB0ZXN0LmNhdGVnb3J5LFxuICAgIHVybDogdGVzdC51cmwsXG4gICAgaW1hZ2U6IHRlc3QuaW1hZ2UsXG4gICAgYnJhbmQ6IHtcbiAgICAgICdAdHlwZSc6ICdCcmFuZCcsXG4gICAgICBuYW1lOiAnTXlzdGljYWwgV2Vic2l0ZScsXG4gICAgfSxcbiAgICBvZmZlcnM6IHtcbiAgICAgICdAdHlwZSc6ICdPZmZlcicsXG4gICAgICBwcmljZTogJzAnLFxuICAgICAgcHJpY2VDdXJyZW5jeTogJ1VTRCcsXG4gICAgICBhdmFpbGFiaWxpdHk6ICdodHRwczovL3NjaGVtYS5vcmcvSW5TdG9jaycsXG4gICAgfSxcbiAgfSk7XG59XG5cbi8qKlxuICog55Sf5oiQ6Z2i5YyF5bGR57uT5p6E5YyW5pWw5o2uXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBnZW5lcmF0ZUJyZWFkY3J1bWJTdHJ1Y3R1cmVkRGF0YShicmVhZGNydW1iczogQXJyYXk8eyBuYW1lOiBzdHJpbmc7IHVybDogc3RyaW5nIH0+KSB7XG4gIHJldHVybiBnZW5lcmF0ZVN0cnVjdHVyZWREYXRhKCdCcmVhZGNydW1iTGlzdCcsIHtcbiAgICBpdGVtTGlzdEVsZW1lbnQ6IGJyZWFkY3J1bWJzLm1hcCgoaXRlbSwgaW5kZXgpID0+ICh7XG4gICAgICAnQHR5cGUnOiAnTGlzdEl0ZW0nLFxuICAgICAgcG9zaXRpb246IGluZGV4ICsgMSxcbiAgICAgIG5hbWU6IGl0ZW0ubmFtZSxcbiAgICAgIGl0ZW06IGl0ZW0udXJsLFxuICAgIH0pKSxcbiAgfSk7XG59XG5cbi8qKlxuICog55Sf5oiQRkFR57uT5p6E5YyW5pWw5o2uXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBnZW5lcmF0ZUZBUVN0cnVjdHVyZWREYXRhKGZhcXM6IEFycmF5PHsgcXVlc3Rpb246IHN0cmluZzsgYW5zd2VyOiBzdHJpbmcgfT4pIHtcbiAgcmV0dXJuIGdlbmVyYXRlU3RydWN0dXJlZERhdGEoJ0ZBUVBhZ2UnLCB7XG4gICAgbWFpbkVudGl0eTogZmFxcy5tYXAoZmFxID0+ICh7XG4gICAgICAnQHR5cGUnOiAnUXVlc3Rpb24nLFxuICAgICAgbmFtZTogZmFxLnF1ZXN0aW9uLFxuICAgICAgYWNjZXB0ZWRBbnN3ZXI6IHtcbiAgICAgICAgJ0B0eXBlJzogJ0Fuc3dlcicsXG4gICAgICAgIHRleHQ6IGZhcS5hbnN3ZXIsXG4gICAgICB9LFxuICAgIH0pKSxcbiAgfSk7XG59XG5cbi8qKlxuICog55Sf5oiQ57uE57uH57uT5p6E5YyW5pWw5o2uXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBnZW5lcmF0ZU9yZ2FuaXphdGlvblN0cnVjdHVyZWREYXRhKCkge1xuICByZXR1cm4gZ2VuZXJhdGVTdHJ1Y3R1cmVkRGF0YSgnT3JnYW5pemF0aW9uJywge1xuICAgIG5hbWU6ICdNeXN0aWNhbCBXZWJzaXRlJyxcbiAgICBkZXNjcmlwdGlvbjogJ1Byb2Zlc3Npb25hbCBteXN0aWNhbCBwbGF0Zm9ybSBvZmZlcmluZyBmcmVlIHRhcm90LCBhc3Ryb2xvZ3ksIGFuZCBudW1lcm9sb2d5IHRlc3RzLicsXG4gICAgdXJsOiBwcm9jZXNzLmVudlsnTkVYVF9QVUJMSUNfQVBQX1VSTCddIHx8ICdodHRwczovL215c3RpY2FsLXdlYnNpdGUuY29tJyxcbiAgICBsb2dvOiBgJHtwcm9jZXNzLmVudlsnTkVYVF9QVUJMSUNfQVBQX1VSTCddIHx8ICdodHRwczovL215c3RpY2FsLXdlYnNpdGUuY29tJ30vaW1hZ2VzL2xvZ28ucG5nYCxcbiAgICBzYW1lQXM6IFtcbiAgICAgICdodHRwczovL3R3aXR0ZXIuY29tL215c3RpY2FsX3dlYnNpdGUnLFxuICAgICAgJ2h0dHBzOi8vZmFjZWJvb2suY29tL215c3RpY2FsX3dlYnNpdGUnLFxuICAgICAgJ2h0dHBzOi8vaW5zdGFncmFtLmNvbS9teXN0aWNhbF93ZWJzaXRlJyxcbiAgICBdLFxuICAgIGNvbnRhY3RQb2ludDoge1xuICAgICAgJ0B0eXBlJzogJ0NvbnRhY3RQb2ludCcsXG4gICAgICBjb250YWN0VHlwZTogJ0N1c3RvbWVyIFNlcnZpY2UnLFxuICAgICAgZW1haWw6ICdjb250YWN0QG15c3RpY2FsLXdlYnNpdGUuY29tJyxcbiAgICB9LFxuICB9KTtcbn1cblxuLyoqXG4gKiDkvJjljJbmoIfpopjplb/luqZcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIG9wdGltaXplVGl0bGUodGl0bGU6IHN0cmluZywgbWF4TGVuZ3RoOiBudW1iZXIgPSA2MCk6IHN0cmluZyB7XG4gIGlmICh0aXRsZS5sZW5ndGggPD0gbWF4TGVuZ3RoKSByZXR1cm4gdGl0bGU7XG4gIFxuICAvLyDlsJ3or5XlnKjljZXor43ovrnnlYzmiKrmlq1cbiAgY29uc3QgdHJ1bmNhdGVkID0gdGl0bGUuc2xpY2UoMCwgbWF4TGVuZ3RoKTtcbiAgY29uc3QgbGFzdFNwYWNlID0gdHJ1bmNhdGVkLmxhc3RJbmRleE9mKCcgJyk7XG4gIFxuICBpZiAobGFzdFNwYWNlID4gbWF4TGVuZ3RoICogMC44KSB7XG4gICAgcmV0dXJuIHRydW5jYXRlZC5zbGljZSgwLCBsYXN0U3BhY2UpICsgJy4uLic7XG4gIH1cbiAgXG4gIHJldHVybiB0cnVuY2F0ZWQuc2xpY2UoMCwgbWF4TGVuZ3RoIC0gMykgKyAnLi4uJztcbn1cblxuLyoqXG4gKiDkvJjljJbmj4/ov7Dplb/luqZcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIG9wdGltaXplRGVzY3JpcHRpb24oZGVzY3JpcHRpb246IHN0cmluZywgbWF4TGVuZ3RoOiBudW1iZXIgPSAxNjApOiBzdHJpbmcge1xuICBpZiAoZGVzY3JpcHRpb24ubGVuZ3RoIDw9IG1heExlbmd0aCkgcmV0dXJuIGRlc2NyaXB0aW9uO1xuICBcbiAgLy8g5bCd6K+V5Zyo5Y+l5a2Q6L6555WM5oiq5patXG4gIGNvbnN0IHNlbnRlbmNlcyA9IGRlc2NyaXB0aW9uLnNwbGl0KCcuICcpO1xuICBsZXQgcmVzdWx0ID0gJyc7XG4gIFxuICBmb3IgKGNvbnN0IHNlbnRlbmNlIG9mIHNlbnRlbmNlcykge1xuICAgIGlmICgocmVzdWx0ICsgc2VudGVuY2UgKyAnLiAnKS5sZW5ndGggPD0gbWF4TGVuZ3RoKSB7XG4gICAgICByZXN1bHQgKz0gc2VudGVuY2UgKyAnLiAnO1xuICAgIH0gZWxzZSB7XG4gICAgICBicmVhaztcbiAgICB9XG4gIH1cbiAgXG4gIGlmIChyZXN1bHQubGVuZ3RoID4gMCkge1xuICAgIHJldHVybiByZXN1bHQudHJpbSgpO1xuICB9XG4gIFxuICAvLyDlpoLmnpzmsqHmnInlkIjpgILnmoTlj6XlrZDovrnnlYzvvIzlnKjljZXor43ovrnnlYzmiKrmlq1cbiAgY29uc3QgdHJ1bmNhdGVkID0gZGVzY3JpcHRpb24uc2xpY2UoMCwgbWF4TGVuZ3RoKTtcbiAgY29uc3QgbGFzdFNwYWNlID0gdHJ1bmNhdGVkLmxhc3RJbmRleE9mKCcgJyk7XG4gIFxuICBpZiAobGFzdFNwYWNlID4gbWF4TGVuZ3RoICogMC44KSB7XG4gICAgcmV0dXJuIHRydW5jYXRlZC5zbGljZSgwLCBsYXN0U3BhY2UpICsgJy4uLic7XG4gIH1cbiAgXG4gIHJldHVybiB0cnVuY2F0ZWQuc2xpY2UoMCwgbWF4TGVuZ3RoIC0gMykgKyAnLi4uJztcbn1cbiJdLCJuYW1lcyI6WyJnZW5lcmF0ZU1ldGFkYXRhIiwiY29uZmlnIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsImtleXdvcmRzIiwiaW1hZ2UiLCJ1cmwiLCJ0eXBlIiwibG9jYWxlIiwic2l0ZU5hbWUiLCJhdXRob3IiLCJwdWJsaXNoZWRUaW1lIiwibW9kaWZpZWRUaW1lIiwic2VjdGlvbiIsInRhZ3MiLCJtZXRhZGF0YSIsImpvaW4iLCJhdXRob3JzIiwibmFtZSIsIm9wZW5HcmFwaCIsImltYWdlcyIsImFsdCIsImxlbmd0aCIsInR3aXR0ZXIiLCJjYXJkIiwiY3JlYXRvciIsInJvYm90cyIsImluZGV4IiwiZm9sbG93IiwiZ29vZ2xlQm90IiwidmVyaWZpY2F0aW9uIiwiZ29vZ2xlIiwicHJvY2VzcyIsImVudiIsIm90aGVyIiwiZ2VuZXJhdGVTdHJ1Y3R1cmVkRGF0YSIsImRhdGEiLCJiYXNlU3RydWN0dXJlIiwiSlNPTiIsInN0cmluZ2lmeSIsImdlbmVyYXRlV2Vic2l0ZVN0cnVjdHVyZWREYXRhIiwiaW5MYW5ndWFnZSIsInBvdGVudGlhbEFjdGlvbiIsInRhcmdldCIsImdlbmVyYXRlQXJ0aWNsZVN0cnVjdHVyZWREYXRhIiwiYXJ0aWNsZSIsImhlYWRsaW5lIiwicHVibGlzaGVyIiwibG9nbyIsImRhdGVQdWJsaXNoZWQiLCJkYXRlTW9kaWZpZWQiLCJtYWluRW50aXR5T2ZQYWdlIiwiYXJ0aWNsZVNlY3Rpb24iLCJjYXRlZ29yeSIsImdlbmVyYXRlVGVzdFN0cnVjdHVyZWREYXRhIiwidGVzdCIsImJyYW5kIiwib2ZmZXJzIiwicHJpY2UiLCJwcmljZUN1cnJlbmN5IiwiYXZhaWxhYmlsaXR5IiwiZ2VuZXJhdGVCcmVhZGNydW1iU3RydWN0dXJlZERhdGEiLCJicmVhZGNydW1icyIsIml0ZW1MaXN0RWxlbWVudCIsIm1hcCIsIml0ZW0iLCJwb3NpdGlvbiIsImdlbmVyYXRlRkFRU3RydWN0dXJlZERhdGEiLCJmYXFzIiwibWFpbkVudGl0eSIsImZhcSIsInF1ZXN0aW9uIiwiYWNjZXB0ZWRBbnN3ZXIiLCJ0ZXh0IiwiYW5zd2VyIiwiZ2VuZXJhdGVPcmdhbml6YXRpb25TdHJ1Y3R1cmVkRGF0YSIsInNhbWVBcyIsImNvbnRhY3RQb2ludCIsImNvbnRhY3RUeXBlIiwiZW1haWwiLCJvcHRpbWl6ZVRpdGxlIiwibWF4TGVuZ3RoIiwidHJ1bmNhdGVkIiwic2xpY2UiLCJsYXN0U3BhY2UiLCJsYXN0SW5kZXhPZiIsIm9wdGltaXplRGVzY3JpcHRpb24iLCJzZW50ZW5jZXMiLCJzcGxpdCIsInJlc3VsdCIsInNlbnRlbmNlIiwidHJpbSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/seo.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   copyToClipboard: () => (/* binding */ copyToClipboard),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   deepClone: () => (/* binding */ deepClone),\n/* harmony export */   delay: () => (/* binding */ delay),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatNumber: () => (/* binding */ formatNumber),\n/* harmony export */   generateId: () => (/* binding */ generateId),\n/* harmony export */   generateSlug: () => (/* binding */ generateSlug),\n/* harmony export */   getDeviceType: () => (/* binding */ getDeviceType),\n/* harmony export */   getRelativeTime: () => (/* binding */ getRelativeTime),\n/* harmony export */   isMobile: () => (/* binding */ isMobile),\n/* harmony export */   isValidEmail: () => (/* binding */ isValidEmail),\n/* harmony export */   throttle: () => (/* binding */ throttle),\n/* harmony export */   truncateText: () => (/* binding */ truncateText)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(rsc)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(rsc)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\n/**\n * 合并Tailwind CSS类名\n */ function cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n/**\n * 格式化日期\n */ function formatDate(date, locale = \"en-US\", options = {}) {\n    const dateObj = typeof date === \"string\" ? new Date(date) : date;\n    const defaultOptions = {\n        year: \"numeric\",\n        month: \"long\",\n        day: \"numeric\"\n    };\n    return new Intl.DateTimeFormat(locale, {\n        ...defaultOptions,\n        ...options\n    }).format(dateObj);\n}\n/**\n * 生成SEO友好的URL slug\n */ function generateSlug(text) {\n    return text.toLowerCase().trim().replace(/[^\\w\\s-]/g, \"\") // 移除特殊字符\n    .replace(/[\\s_-]+/g, \"-\") // 替换空格和下划线为连字符\n    .replace(/^-+|-+$/g, \"\"); // 移除开头和结尾的连字符\n}\n/**\n * 截断文本\n */ function truncateText(text, maxLength) {\n    if (text.length <= maxLength) return text;\n    return text.slice(0, maxLength).trim() + \"...\";\n}\n/**\n * 延迟函数\n */ function delay(ms) {\n    return new Promise((resolve)=>setTimeout(resolve, ms));\n}\n/**\n * 防抖函数\n */ function debounce(func, wait) {\n    let timeout;\n    return (...args)=>{\n        clearTimeout(timeout);\n        timeout = setTimeout(()=>func(...args), wait);\n    };\n}\n/**\n * 节流函数\n */ function throttle(func, limit) {\n    let inThrottle;\n    return (...args)=>{\n        if (!inThrottle) {\n            func(...args);\n            inThrottle = true;\n            setTimeout(()=>inThrottle = false, limit);\n        }\n    };\n}\n/**\n * 生成随机ID\n */ function generateId(length = 8) {\n    const chars = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789\";\n    let result = \"\";\n    for(let i = 0; i < length; i++){\n        result += chars.charAt(Math.floor(Math.random() * chars.length));\n    }\n    return result;\n}\n/**\n * 验证邮箱格式\n */ function isValidEmail(email) {\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n}\n/**\n * 获取相对时间\n */ function getRelativeTime(date, locale = \"en\") {\n    const dateObj = typeof date === \"string\" ? new Date(date) : date;\n    const now = new Date();\n    const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000);\n    const rtf = new Intl.RelativeTimeFormat(locale, {\n        numeric: \"auto\"\n    });\n    if (diffInSeconds < 60) {\n        return rtf.format(-diffInSeconds, \"second\");\n    } else if (diffInSeconds < 3600) {\n        return rtf.format(-Math.floor(diffInSeconds / 60), \"minute\");\n    } else if (diffInSeconds < 86400) {\n        return rtf.format(-Math.floor(diffInSeconds / 3600), \"hour\");\n    } else if (diffInSeconds < 2592000) {\n        return rtf.format(-Math.floor(diffInSeconds / 86400), \"day\");\n    } else if (diffInSeconds < 31536000) {\n        return rtf.format(-Math.floor(diffInSeconds / 2592000), \"month\");\n    } else {\n        return rtf.format(-Math.floor(diffInSeconds / 31536000), \"year\");\n    }\n}\n/**\n * 深度克隆对象\n */ function deepClone(obj) {\n    if (obj === null || typeof obj !== \"object\") return obj;\n    if (obj instanceof Date) return new Date(obj.getTime());\n    if (obj instanceof Array) return obj.map((item)=>deepClone(item));\n    if (typeof obj === \"object\") {\n        const clonedObj = {};\n        for(const key in obj){\n            if (obj.hasOwnProperty(key)) {\n                clonedObj[key] = deepClone(obj[key]);\n            }\n        }\n        return clonedObj;\n    }\n    return obj;\n}\n/**\n * 格式化数字\n */ function formatNumber(number, locale = \"en-US\", options = {}) {\n    return new Intl.NumberFormat(locale, options).format(number);\n}\n/**\n * 检查是否为移动设备\n */ function isMobile() {\n    if (true) return false;\n    return window.innerWidth < 768;\n}\n/**\n * 获取设备类型\n */ function getDeviceType() {\n    if (true) return \"desktop\";\n    const width = window.innerWidth;\n    if (width < 768) return \"mobile\";\n    if (width < 1024) return \"tablet\";\n    return \"desktop\";\n}\n/**\n * 复制文本到剪贴板\n */ async function copyToClipboard(text) {\n    try {\n        if (navigator.clipboard && window.isSecureContext) {\n            await navigator.clipboard.writeText(text);\n            return true;\n        } else {\n            // 降级方案\n            const textArea = document.createElement(\"textarea\");\n            textArea.value = text;\n            textArea.style.position = \"fixed\";\n            textArea.style.left = \"-999999px\";\n            textArea.style.top = \"-999999px\";\n            document.body.appendChild(textArea);\n            textArea.focus();\n            textArea.select();\n            const result = document.execCommand(\"copy\");\n            textArea.remove();\n            return result;\n        }\n    } catch (error) {\n        console.error(\"Failed to copy text:\", error);\n        return false;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/utils.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/@opentelemetry","vendor-chunks/@formatjs","vendor-chunks/next-intl","vendor-chunks/use-intl","vendor-chunks/lucide-react","vendor-chunks/intl-messageformat","vendor-chunks/@radix-ui","vendor-chunks/tslib","vendor-chunks/tailwind-merge","vendor-chunks/clsx","vendor-chunks/class-variance-authority"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F%5Blocale%5D%2Fpage&page=%2F%5Blocale%5D%2Fpage&appPaths=%2F%5Blocale%5D%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Fpage.tsx&appDir=E%3A%5Ctarot-seo%5Csrc%5Capp&pageExtensions=ts&pageExtensions=tsx&pageExtensions=js&pageExtensions=jsx&pageExtensions=md&pageExtensions=mdx&rootDir=E%3A%5Ctarot-seo&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/_not-found/page";
exports.ids = ["app/_not-found/page"];
exports.modules = {

/***/ "(rsc)/./messages lazy recursive ^\\.\\/.*\\.json$":
/*!********************************************************!*\
  !*** ./messages/ lazy ^\.\/.*\.json$ namespace object ***!
  \********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var map = {
	"./en.json": [
		"(rsc)/./messages/en.json",
		"_rsc_messages_en_json"
	],
	"./es.json": [
		"(rsc)/./messages/es.json",
		"_rsc_messages_es_json"
	],
	"./hi.json": [
		"(rsc)/./messages/hi.json",
		"_rsc_messages_hi_json"
	],
	"./ja.json": [
		"(rsc)/./messages/ja.json",
		"_rsc_messages_ja_json"
	],
	"./pt.json": [
		"(rsc)/./messages/pt.json",
		"_rsc_messages_pt_json"
	],
	"./zh.json": [
		"(rsc)/./messages/zh.json",
		"_rsc_messages_zh_json"
	]
};
function webpackAsyncContext(req) {
	if(!__webpack_require__.o(map, req)) {
		return Promise.resolve().then(() => {
			var e = new Error("Cannot find module '" + req + "'");
			e.code = 'MODULE_NOT_FOUND';
			throw e;
		});
	}

	var ids = map[req], id = ids[0];
	return __webpack_require__.e(ids[1]).then(() => {
		return __webpack_require__.t(id, 3 | 16);
	});
}
webpackAsyncContext.keys = () => (Object.keys(map));
webpackAsyncContext.id = "(rsc)/./messages lazy recursive ^\\.\\/.*\\.json$";
module.exports = webpackAsyncContext;

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=E%3A%5Ctarot-seo%5Csrc%5Capp&pageExtensions=ts&pageExtensions=tsx&pageExtensions=js&pageExtensions=jsx&pageExtensions=md&pageExtensions=mdx&rootDir=E%3A%5Ctarot-seo&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=E%3A%5Ctarot-seo%5Csrc%5Capp&pageExtensions=ts&pageExtensions=tsx&pageExtensions=js&pageExtensions=jsx&pageExtensions=md&pageExtensions=mdx&rootDir=E%3A%5Ctarot-seo&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n          children: [\"/_not-found\", {\n            children: ['__PAGE__', {}, {\n              page: [\n                () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)),\n                \"next/dist/client/components/not-found-error\"\n              ]\n            }]\n          }, {}]\n        },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"E:\\\\tarot-seo\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/_not-found/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/_not-found/page\",\n        pathname: \"/_not-found\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=E%3A%5Ctarot-seo%5Csrc%5Capp&pageExtensions=ts&pageExtensions=tsx&pageExtensions=js&pageExtensions=jsx&pageExtensions=md&pageExtensions=mdx&rootDir=E%3A%5Ctarot-seo&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUN0YXJvdC1zZW8lNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNhcHAtcm91dGVyLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUN0YXJvdC1zZW8lNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNjbGllbnQtcGFnZS5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJFJTNBJTVDJTVDdGFyb3Qtc2VvJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDZXJyb3ItYm91bmRhcnkuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRSUzQSU1QyU1Q3Rhcm90LXNlbyU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2xheW91dC1yb3V0ZXIuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRSUzQSU1QyU1Q3Rhcm90LXNlbyU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q25vdC1mb3VuZC1ib3VuZGFyeS5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJFJTNBJTVDJTVDdGFyb3Qtc2VvJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDcmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa09BQStHO0FBQy9HO0FBQ0Esb09BQWdIO0FBQ2hIO0FBQ0EsME9BQW1IO0FBQ25IO0FBQ0Esd09BQWtIO0FBQ2xIO0FBQ0Esa1BBQXVIO0FBQ3ZIO0FBQ0Esc1FBQWlJIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbXlzdGljYWwtd2Vic2l0ZS8/MzljZiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkU6XFxcXHRhcm90LXNlb1xcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGFwcC1yb3V0ZXIuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkU6XFxcXHRhcm90LXNlb1xcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGNsaWVudC1wYWdlLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJFOlxcXFx0YXJvdC1zZW9cXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxlcnJvci1ib3VuZGFyeS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRTpcXFxcdGFyb3Qtc2VvXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcbGF5b3V0LXJvdXRlci5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRTpcXFxcdGFyb3Qtc2VvXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcbm90LWZvdW5kLWJvdW5kYXJ5LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJFOlxcXFx0YXJvdC1zZW9cXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxyZW5kZXItZnJvbS10ZW1wbGF0ZS1jb250ZXh0LmpzXCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Noto_Sans_SC%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-noto-sans-sc%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22notoSansSC%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Noto_Sans_JP%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-noto-sans-jp%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22notoSansJP%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Noto_Sans%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%2C%5C%22devanagari%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-noto-sans%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22notoSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Csrc%5C%5Cstyles%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Noto_Sans_SC%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-noto-sans-sc%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22notoSansSC%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Noto_Sans_JP%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-noto-sans-jp%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22notoSansJP%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Noto_Sans%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%2C%5C%22devanagari%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-noto-sans%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22notoSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Csrc%5C%5Cstyles%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"fb7eb1c86c9a\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvc3R5bGVzL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbXlzdGljYWwtd2Vic2l0ZS8uL3NyYy9zdHlsZXMvZ2xvYmFscy5jc3M/MTA2ZiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImZiN2ViMWM4NmM5YVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/styles/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\",\"display\":\"swap\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-inter\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Noto_Sans_SC_arguments_subsets_latin_weight_400_500_700_variable_font_noto_sans_sc_display_swap_variableName_notoSansSC___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Noto_Sans_SC\",\"arguments\":[{\"subsets\":[\"latin\"],\"weight\":[\"400\",\"500\",\"700\"],\"variable\":\"--font-noto-sans-sc\",\"display\":\"swap\"}],\"variableName\":\"notoSansSC\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Noto_Sans_SC\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"weight\\\":[\\\"400\\\",\\\"500\\\",\\\"700\\\"],\\\"variable\\\":\\\"--font-noto-sans-sc\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"notoSansSC\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Noto_Sans_SC_arguments_subsets_latin_weight_400_500_700_variable_font_noto_sans_sc_display_swap_variableName_notoSansSC___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Noto_Sans_SC_arguments_subsets_latin_weight_400_500_700_variable_font_noto_sans_sc_display_swap_variableName_notoSansSC___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Noto_Sans_JP_arguments_subsets_latin_weight_400_500_700_variable_font_noto_sans_jp_display_swap_variableName_notoSansJP___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Noto_Sans_JP\",\"arguments\":[{\"subsets\":[\"latin\"],\"weight\":[\"400\",\"500\",\"700\"],\"variable\":\"--font-noto-sans-jp\",\"display\":\"swap\"}],\"variableName\":\"notoSansJP\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Noto_Sans_JP\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"weight\\\":[\\\"400\\\",\\\"500\\\",\\\"700\\\"],\\\"variable\\\":\\\"--font-noto-sans-jp\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"notoSansJP\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Noto_Sans_JP_arguments_subsets_latin_weight_400_500_700_variable_font_noto_sans_jp_display_swap_variableName_notoSansJP___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Noto_Sans_JP_arguments_subsets_latin_weight_400_500_700_variable_font_noto_sans_jp_display_swap_variableName_notoSansJP___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Noto_Sans_arguments_subsets_latin_devanagari_weight_400_500_700_variable_font_noto_sans_display_swap_variableName_notoSans___WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Noto_Sans\",\"arguments\":[{\"subsets\":[\"latin\",\"devanagari\"],\"weight\":[\"400\",\"500\",\"700\"],\"variable\":\"--font-noto-sans\",\"display\":\"swap\"}],\"variableName\":\"notoSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Noto_Sans\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\",\\\"devanagari\\\"],\\\"weight\\\":[\\\"400\\\",\\\"500\\\",\\\"700\\\"],\\\"variable\\\":\\\"--font-noto-sans\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"notoSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Noto_Sans_arguments_subsets_latin_devanagari_weight_400_500_700_variable_font_noto_sans_display_swap_variableName_notoSans___WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Noto_Sans_arguments_subsets_latin_devanagari_weight_400_500_700_variable_font_noto_sans_display_swap_variableName_notoSans___WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/styles/globals.css */ \"(rsc)/./src/styles/globals.css\");\n/* harmony import */ var _i18n__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/i18n */ \"(rsc)/./src/i18n.ts\");\n\n\n\n\n\n\n\n// 基础元数据\nconst metadata = {\n    title: \"Mystical Website - Free Tarot, Astrology & Numerology Tests\",\n    description: \"Professional online mystical platform offering free tarot, astrology, and numerology tests with AI-powered analysis. Accurate personality insights and life guidance.\",\n    keywords: \"mystical tests, free tests, AI analysis, tarot, astrology, numerology\",\n    authors: [\n        {\n            name: \"Mystical Website Team\"\n        }\n    ],\n    creator: \"Mystical Website\",\n    publisher: \"Mystical Website\",\n    robots: {\n        index: true,\n        follow: true,\n        googleBot: {\n            index: true,\n            follow: true,\n            \"max-video-preview\": -1,\n            \"max-image-preview\": \"large\",\n            \"max-snippet\": -1\n        }\n    },\n    openGraph: {\n        type: \"website\",\n        locale: \"en_US\",\n        url: \"http://localhost:3000\" || 0,\n        siteName: \"Mystical Website\",\n        title: \"Mystical Website - Free Tarot, Astrology & Numerology Tests\",\n        description: \"Professional online mystical platform offering free tarot, astrology, and numerology tests with AI-powered analysis.\"\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"Mystical Website - Free Tarot, Astrology & Numerology Tests\",\n        description: \"Professional online mystical platform offering free tarot, astrology, and numerology tests with AI-powered analysis.\",\n        creator: \"@mystical_website\"\n    },\n    verification: {\n        google: process.env[\"NEXT_PUBLIC_GOOGLE_SITE_VERIFICATION\"] || null,\n        other: {\n            \"msvalidate.01\": process.env[\"NEXT_PUBLIC_BING_SITE_VERIFICATION\"] || \"\"\n        }\n    },\n    // 添加多语言替代链接\n    alternates: {\n        canonical: \"http://localhost:3000\" || 0,\n        languages: Object.fromEntries(_i18n__WEBPACK_IMPORTED_MODULE_2__.locales.map((locale)=>[\n                _i18n__WEBPACK_IMPORTED_MODULE_2__.languageConfig[locale].hreflang,\n                `${\"http://localhost:3000\" || 0}/${locale}`\n            ]))\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        suppressHydrationWarning: true,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preload\",\n                        href: \"/fonts/inter-var.woff2\",\n                        as: \"font\",\n                        type: \"font/woff2\",\n                        crossOrigin: \"anonymous\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"dns-prefetch\",\n                        href: \"//fonts.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"dns-prefetch\",\n                        href: \"//fonts.gstatic.com\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"dns-prefetch\",\n                        href: \"//api.mystical-website.com\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 9\n                    }, this),\n                    _i18n__WEBPACK_IMPORTED_MODULE_2__.locales.map((locale)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"alternate\",\n                            hrefLang: _i18n__WEBPACK_IMPORTED_MODULE_2__.languageConfig[locale].hreflang,\n                            href: `${\"http://localhost:3000\" || 0}/${locale}`\n                        }, locale, false, {\n                            fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 11\n                        }, this)),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"alternate\",\n                        hrefLang: \"x-default\",\n                        href: \"http://localhost:3000\" || 0\n                    }, void 0, false, {\n                        fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 93,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `\n          ${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().variable)}\n          ${(next_font_google_target_css_path_src_app_layout_tsx_import_Noto_Sans_SC_arguments_subsets_latin_weight_400_500_700_variable_font_noto_sans_sc_display_swap_variableName_notoSansSC___WEBPACK_IMPORTED_MODULE_4___default().variable)}\n          ${(next_font_google_target_css_path_src_app_layout_tsx_import_Noto_Sans_JP_arguments_subsets_latin_weight_400_500_700_variable_font_noto_sans_jp_display_swap_variableName_notoSansJP___WEBPACK_IMPORTED_MODULE_5___default().variable)}\n          ${(next_font_google_target_css_path_src_app_layout_tsx_import_Noto_Sans_arguments_subsets_latin_devanagari_weight_400_500_700_variable_font_noto_sans_display_swap_variableName_notoSans___WEBPACK_IMPORTED_MODULE_6___default().variable)}\n          font-sans antialiased\n        `,\n                children: children\n            }, void 0, false, {\n                fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 122,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 92,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/i18n.ts":
/*!*********************!*\
  !*** ./src/i18n.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   defaultLocale: () => (/* binding */ defaultLocale),\n/* harmony export */   fallbackChain: () => (/* binding */ fallbackChain),\n/* harmony export */   formatConfig: () => (/* binding */ formatConfig),\n/* harmony export */   languageConfig: () => (/* binding */ languageConfig),\n/* harmony export */   locales: () => (/* binding */ locales),\n/* harmony export */   pathnames: () => (/* binding */ pathnames),\n/* harmony export */   phase2Locales: () => (/* binding */ phase2Locales),\n/* harmony export */   rtlLocales: () => (/* binding */ rtlLocales)\n/* harmony export */ });\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/navigation */ \"(rsc)/./node_modules/next/dist/api/navigation.react-server.js\");\n/* harmony import */ var next_intl_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-intl/server */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getRequestConfig.js\");\n\n\n// 第一阶段：核心市场语言 (立即实施)\nconst locales = [\n    \"en\",\n    \"zh\",\n    \"es\",\n    \"pt\",\n    \"hi\",\n    \"ja\"\n];\n// 第二阶段：重要区域语言 (6个月后扩展)\nconst phase2Locales = [\n    \"de\",\n    \"fr\",\n    \"it\",\n    \"ru\",\n    \"ko\",\n    \"ar\"\n];\n// 语言配置 - 包含完整的多语言元数据\nconst languageConfig = {\n    en: {\n        name: \"English\",\n        nativeName: \"English\",\n        direction: \"ltr\",\n        region: \"Global\",\n        population: \"Global market\",\n        flag: \"\\uD83C\\uDDFA\\uD83C\\uDDF8\",\n        hreflang: \"en\",\n        locale: \"en-US\",\n        currency: \"USD\",\n        fontFamily: \"latin\",\n        expansionFactor: 1.0,\n        priority: 1\n    },\n    zh: {\n        name: \"Chinese\",\n        nativeName: \"中文\",\n        direction: \"ltr\",\n        region: \"China & Chinese communities\",\n        population: \"1.4 billion\",\n        flag: \"\\uD83C\\uDDE8\\uD83C\\uDDF3\",\n        hreflang: \"zh-CN\",\n        locale: \"zh-CN\",\n        currency: \"CNY\",\n        fontFamily: \"chinese\",\n        expansionFactor: 0.8,\n        priority: 2\n    },\n    es: {\n        name: \"Spanish\",\n        nativeName: \"Espa\\xf1ol\",\n        direction: \"ltr\",\n        region: \"Spain & Latin America\",\n        population: \"500 million\",\n        flag: \"\\uD83C\\uDDEA\\uD83C\\uDDF8\",\n        hreflang: \"es\",\n        locale: \"es-ES\",\n        currency: \"EUR\",\n        fontFamily: \"latin\",\n        expansionFactor: 1.25,\n        priority: 3\n    },\n    pt: {\n        name: \"Portuguese\",\n        nativeName: \"Portugu\\xeas\",\n        direction: \"ltr\",\n        region: \"Brazil & Portuguese-speaking countries\",\n        population: \"260 million\",\n        flag: \"\\uD83C\\uDDE7\\uD83C\\uDDF7\",\n        hreflang: \"pt-BR\",\n        locale: \"pt-BR\",\n        currency: \"BRL\",\n        fontFamily: \"latin\",\n        expansionFactor: 1.20,\n        priority: 4\n    },\n    hi: {\n        name: \"Hindi\",\n        nativeName: \"हिन्दी\",\n        direction: \"ltr\",\n        region: \"Northern India\",\n        population: \"600 million\",\n        flag: \"\\uD83C\\uDDEE\\uD83C\\uDDF3\",\n        hreflang: \"hi-IN\",\n        locale: \"hi-IN\",\n        currency: \"INR\",\n        fontFamily: \"hindi\",\n        expansionFactor: 1.40,\n        priority: 5\n    },\n    ja: {\n        name: \"Japanese\",\n        nativeName: \"日本語\",\n        direction: \"ltr\",\n        region: \"Japan\",\n        population: \"125 million\",\n        flag: \"\\uD83C\\uDDEF\\uD83C\\uDDF5\",\n        hreflang: \"ja-JP\",\n        locale: \"ja-JP\",\n        currency: \"JPY\",\n        fontFamily: \"japanese\",\n        expansionFactor: 1.10,\n        priority: 6\n    }\n};\n// 默认语言\nconst defaultLocale = \"en\";\n// RTL语言配置\nconst rtlLocales = [\n    \"ar\",\n    \"he\",\n    \"fa\",\n    \"ur\"\n];\n// 语言回退链配置\nconst fallbackChain = {\n    en: [],\n    zh: [\n        \"en\"\n    ],\n    es: [\n        \"en\"\n    ],\n    pt: [\n        \"es\",\n        \"en\"\n    ],\n    hi: [\n        \"en\"\n    ],\n    ja: [\n        \"en\"\n    ]\n};\n// 语言检测配置 - 增强版本，支持回退机制\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_intl_server__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(async ({ locale })=>{\n    // 验证传入的语言是否支持\n    if (!locales.includes(locale)) {\n        (0,next_navigation__WEBPACK_IMPORTED_MODULE_0__.notFound)();\n    }\n    try {\n        // 尝试加载主要语言文件\n        const messages = (await __webpack_require__(\"(rsc)/./messages lazy recursive ^\\\\.\\\\/.*\\\\.json$\")(`./${locale}.json`)).default;\n        // 如果有回退语言，合并消息\n        const fallbacks = fallbackChain[locale] || [];\n        let mergedMessages = messages;\n        for (const fallbackLocale of fallbacks){\n            try {\n                const fallbackMessages = (await __webpack_require__(\"(rsc)/./messages lazy recursive ^\\\\.\\\\/.*\\\\.json$\")(`./${fallbackLocale}.json`)).default;\n                mergedMessages = {\n                    ...fallbackMessages,\n                    ...mergedMessages\n                };\n            } catch (fallbackError) {\n                console.warn(`Failed to load fallback messages for locale: ${fallbackLocale}`, fallbackError);\n            }\n        }\n        return {\n            messages: mergedMessages,\n            timeZone: getTimeZoneForLocale(locale),\n            now: new Date(),\n            formats: getFormatsForLocale(locale)\n        };\n    } catch (error) {\n        console.error(`Failed to load messages for locale: ${locale}`, error);\n        // 尝试回退到默认语言\n        if (locale !== defaultLocale) {\n            try {\n                const defaultMessages = (await __webpack_require__(\"(rsc)/./messages lazy recursive ^\\\\.\\\\/.*\\\\.json$\")(`./${defaultLocale}.json`)).default;\n                return {\n                    messages: defaultMessages,\n                    timeZone: getTimeZoneForLocale(defaultLocale),\n                    now: new Date(),\n                    formats: getFormatsForLocale(defaultLocale)\n                };\n            } catch (defaultError) {\n                console.error(`Failed to load default locale messages`, defaultError);\n            }\n        }\n        (0,next_navigation__WEBPACK_IMPORTED_MODULE_0__.notFound)();\n    }\n}));\n// 根据语言获取时区\nfunction getTimeZoneForLocale(locale) {\n    const timezoneMap = {\n        en: \"America/New_York\",\n        zh: \"Asia/Shanghai\",\n        es: \"Europe/Madrid\",\n        pt: \"America/Sao_Paulo\",\n        hi: \"Asia/Kolkata\",\n        ja: \"Asia/Tokyo\"\n    };\n    return timezoneMap[locale] || \"UTC\";\n}\n// 根据语言获取格式化配置\nfunction getFormatsForLocale(locale) {\n    const formatMap = {\n        en: {\n            dateTime: {\n                short: {\n                    day: \"numeric\",\n                    month: \"short\",\n                    year: \"numeric\"\n                },\n                long: {\n                    day: \"numeric\",\n                    month: \"long\",\n                    year: \"numeric\",\n                    weekday: \"long\"\n                }\n            },\n            number: {\n                currency: {\n                    style: \"currency\",\n                    currency: \"USD\"\n                }\n            }\n        },\n        zh: {\n            dateTime: {\n                short: {\n                    day: \"numeric\",\n                    month: \"short\",\n                    year: \"numeric\"\n                },\n                long: {\n                    day: \"numeric\",\n                    month: \"long\",\n                    year: \"numeric\",\n                    weekday: \"long\"\n                }\n            },\n            number: {\n                currency: {\n                    style: \"currency\",\n                    currency: \"CNY\"\n                }\n            }\n        },\n        es: {\n            dateTime: {\n                short: {\n                    day: \"numeric\",\n                    month: \"short\",\n                    year: \"numeric\"\n                },\n                long: {\n                    day: \"numeric\",\n                    month: \"long\",\n                    year: \"numeric\",\n                    weekday: \"long\"\n                }\n            },\n            number: {\n                currency: {\n                    style: \"currency\",\n                    currency: \"EUR\"\n                }\n            }\n        },\n        pt: {\n            dateTime: {\n                short: {\n                    day: \"numeric\",\n                    month: \"short\",\n                    year: \"numeric\"\n                },\n                long: {\n                    day: \"numeric\",\n                    month: \"long\",\n                    year: \"numeric\",\n                    weekday: \"long\"\n                }\n            },\n            number: {\n                currency: {\n                    style: \"currency\",\n                    currency: \"BRL\"\n                }\n            }\n        },\n        hi: {\n            dateTime: {\n                short: {\n                    day: \"numeric\",\n                    month: \"short\",\n                    year: \"numeric\"\n                },\n                long: {\n                    day: \"numeric\",\n                    month: \"long\",\n                    year: \"numeric\",\n                    weekday: \"long\"\n                }\n            },\n            number: {\n                currency: {\n                    style: \"currency\",\n                    currency: \"INR\"\n                }\n            }\n        },\n        ja: {\n            dateTime: {\n                short: {\n                    day: \"numeric\",\n                    month: \"short\",\n                    year: \"numeric\"\n                },\n                long: {\n                    day: \"numeric\",\n                    month: \"long\",\n                    year: \"numeric\",\n                    weekday: \"long\"\n                }\n            },\n            number: {\n                currency: {\n                    style: \"currency\",\n                    currency: \"JPY\"\n                }\n            }\n        }\n    };\n    return formatMap[locale] || formatMap[defaultLocale];\n}\n// 语言路径配置\nconst pathnames = {\n    \"/\": \"/\",\n    \"/blog\": {\n        en: \"/blog\",\n        zh: \"/blog\",\n        es: \"/blog\",\n        pt: \"/blog\",\n        hi: \"/blog\",\n        ja: \"/blog\"\n    },\n    \"/tarot\": {\n        en: \"/tarot\",\n        zh: \"/tarot\",\n        es: \"/tarot\",\n        pt: \"/tarot\",\n        hi: \"/tarot\",\n        ja: \"/tarot\"\n    },\n    \"/astrology\": {\n        en: \"/astrology\",\n        zh: \"/astrology\",\n        es: \"/astrology\",\n        pt: \"/astrology\",\n        hi: \"/astrology\",\n        ja: \"/astrology\"\n    },\n    \"/numerology\": {\n        en: \"/numerology\",\n        zh: \"/numerology\",\n        es: \"/numerology\",\n        pt: \"/numerology\",\n        hi: \"/numerology\",\n        ja: \"/numerology\"\n    }\n};\n// 语言特定的格式化配置\nconst formatConfig = {\n    en: {\n        dateFormat: \"MM/dd/yyyy\",\n        timeFormat: \"12h\",\n        currency: \"USD\",\n        numberFormat: \"en-US\"\n    },\n    zh: {\n        dateFormat: \"yyyy年MM月dd日\",\n        timeFormat: \"24h\",\n        currency: \"CNY\",\n        numberFormat: \"zh-CN\"\n    },\n    es: {\n        dateFormat: \"dd/MM/yyyy\",\n        timeFormat: \"24h\",\n        currency: \"EUR\",\n        numberFormat: \"es-ES\"\n    },\n    pt: {\n        dateFormat: \"dd/MM/yyyy\",\n        timeFormat: \"24h\",\n        currency: \"BRL\",\n        numberFormat: \"pt-BR\"\n    },\n    hi: {\n        dateFormat: \"dd/MM/yyyy\",\n        timeFormat: \"12h\",\n        currency: \"INR\",\n        numberFormat: \"hi-IN\"\n    },\n    ja: {\n        dateFormat: \"yyyy年MM月dd日\",\n        timeFormat: \"24h\",\n        currency: \"JPY\",\n        numberFormat: \"ja-JP\"\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/i18n.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/@opentelemetry","vendor-chunks/next-intl"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=E%3A%5Ctarot-seo%5Csrc%5Capp&pageExtensions=ts&pageExtensions=tsx&pageExtensions=js&pageExtensions=jsx&pageExtensions=md&pageExtensions=mdx&rootDir=E%3A%5Ctarot-seo&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();